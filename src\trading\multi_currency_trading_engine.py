"""
Multi-Currency Trading Engine

Advanced trading engine that enables trading using ALL available currencies
in the portfolio (BTC, ETH, SOL, ADA, DOT, etc.) as base currencies, not just USDT.
Automatically detects and utilizes any cryptocurrency holdings for trading
across all possible currency pairs.

Based on research of professional trading systems including QuantConnect,
Interactive Brokers, DeFi AMMs, and institutional HFT methodologies.
"""

import asyncio
import logging
import time
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from enum import Enum
import time
import itertools

# Import advanced trading components
try:
    from .cross_currency_arbitrage_engine import CrossCurrencyArbitrageEngine, ArbitrageOpportunity
    from .dynamic_portfolio_rebalancer import DynamicPortfolioRebalancer, RebalancingAction
    from .advanced_order_types import AdvancedOrderExecutor, AdvancedOrder, OrderType
    from .intelligent_liquidity_manager import IntelligentLiquidityManager, LiquidityAction
    from .balance_aware_order_manager import BalanceAwareOrderManager, BalanceCheck, OrderSizing
    from .intelligent_currency_switcher import IntelligentCurrencySwitcher
    from .cross_exchange_capital_manager import CrossExchangeCapitalManager, TransferRequest, TransferType
    from .robust_error_recovery import RobustErrorRecovery, ErrorType, RecoveryAction
except ImportError:
    logger.warning("⚠️ [IMPORT] Could not import advanced trading components - some features disabled")
    CrossCurrencyArbitrageEngine = None
    ArbitrageOpportunity = None
    DynamicPortfolioRebalancer = None
    RebalancingAction = None
    AdvancedOrderExecutor = None
    AdvancedOrder = None
    OrderType = None
    IntelligentLiquidityManager = None
    LiquidityAction = None
    BalanceAwareOrderManager = None
    BalanceCheck = None
    OrderSizing = None
    IntelligentCurrencySwitcher = None
    CrossExchangeCapitalManager = None
    TransferRequest = None
    TransferType = None
    RobustErrorRecovery = None
    ErrorType = None
    RecoveryAction = None

logger = logging.getLogger(__name__)

class TradingMode(Enum):
    """Trading modes for multi-currency engine"""
    SINGLE_CURRENCY = "single_currency"  # Traditional USDT-only trading
    MULTI_CURRENCY = "multi_currency"    # Trade with any available currency
    CROSS_CURRENCY = "cross_currency"    # Direct crypto-to-crypto trading
    ARBITRAGE = "arbitrage"              # Cross-exchange and triangular arbitrage

@dataclass
class CurrencyPair:
    """Represents a trading pair with metadata"""
    base: str
    quote: str
    symbol: str
    exchange: str
    min_order_value: Decimal
    tick_size: Decimal
    lot_size: Decimal
    is_active: bool
    liquidity_score: float

@dataclass
class TradingOpportunity:
    """Represents a trading opportunity across currencies"""
    pair: CurrencyPair
    side: str  # 'buy' or 'sell'
    amount: Decimal
    price: Decimal
    expected_profit: Decimal
    confidence: float
    strategy: str
    risk_score: float
    execution_priority: int

class MultiCurrencyTradingEngine:
    """
    Advanced multi-currency trading engine that enables continuous trading
    across all available currencies and exchanges
    """
    
    def __init__(self, exchange_clients: Dict[str, Any], config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}
        
        # Trading configuration
        self.trading_mode = TradingMode.MULTI_CURRENCY
        self.supported_currencies = set()
        self.active_pairs = {}
        self.currency_balances = {}
        
        # Market data and discovery
        self.market_data_cache = {}
        self.pair_discovery_cache = {}
        self.last_discovery_time = 0
        self.discovery_interval = 300  # 5 minutes
        
        # Risk management
        self.max_position_per_currency = Decimal('0.3')  # 30% max per currency
        self.min_liquidity_threshold = 1000.0  # Minimum daily volume
        self.max_correlation_exposure = 0.7  # Maximum correlation exposure
        
        # Performance tracking
        self.execution_stats = {}
        self.profit_tracking = {}

        # ENHANCED PAIR DIVERSIFICATION AND COOLDOWN SYSTEM
        self.pair_cooldowns = {}  # Track recently traded pairs
        self.strategy_cooldowns = {}  # Track recently used strategies
        self.pair_cooldown_duration = 180  # 3 minutes cooldown per pair (reduced for faster rotation)
        self.strategy_cooldown_duration = 60   # 1 minute cooldown per strategy (reduced for faster rotation)
        self.max_consecutive_same_pair = 1     # Max 1 consecutive trade on same pair (force immediate diversification)
        self.consecutive_pair_count = {}  # Track consecutive trades per pair
        self.last_traded_pairs = []  # Track order of recently traded pairs
        self.max_recent_pairs = 15  # Remember last 15 traded pairs for better rotation
        self.current_holdings_count = {}  # Track how many times we hold each currency
        self.recent_trade_history = []  # Track recent trades for strategy balancing
        self.diversification_enforcement = True  # Force diversification
        self.pair_rotation_index = 0  # Index for systematic pair rotation

        # PROFIT MAXIMIZATION TRACKING
        self.profit_tracker = {
            'total_profit_usd': 0.0,
            'total_trades': 0,
            'successful_trades': 0,
            'strategy_performance': {},
            'hourly_profit': [],
            'best_performing_pairs': {},
            'execution_times': []
        }

        # Cross-currency arbitrage engine
        self.arbitrage_engine = None

        # Dynamic portfolio rebalancer
        self.portfolio_rebalancer = None

        # Advanced order executor
        self.order_executor = None

        # Intelligent liquidity manager
        self.liquidity_manager = None

        # Balance-aware order manager
        self.balance_manager = None

        # Intelligent currency switcher
        self.currency_switcher = None

        # Cross-exchange capital manager
        self.capital_manager = None

        # Robust error recovery system
        self.error_recovery = None

        logger.info("🚀 [MULTI-CURRENCY] Initialized advanced multi-currency trading engine")
    
    async def initialize(self):
        """Initialize the multi-currency trading engine"""
        try:
            logger.info("🔧 [MULTI-CURRENCY] Initializing trading engine...")
            
            # Discover all available currencies and pairs
            await self.discover_all_currencies()
            await self.discover_all_trading_pairs()
            
            # Initialize market data feeds
            await self.initialize_market_data_feeds()
            
            # Set up risk management
            await self.initialize_risk_management()

            # Initialize arbitrage engine
            await self.initialize_arbitrage_engine()

            # Initialize portfolio rebalancer
            await self.initialize_portfolio_rebalancer()

            # Initialize advanced order executor
            await self.initialize_order_executor()

            # Initialize intelligent liquidity manager
            await self.initialize_liquidity_manager()

            # Initialize balance-aware order manager
            await self.initialize_balance_manager()

            # Initialize intelligent currency switcher
            await self.initialize_currency_switcher()

            # Initialize cross-exchange capital manager
            await self.initialize_capital_manager()

            # Initialize robust error recovery system
            await self.initialize_error_recovery()

            logger.info(f"✅ [MULTI-CURRENCY] Engine initialized with {len(self.supported_currencies)} currencies")
            logger.info(f"✅ [MULTI-CURRENCY] Active trading pairs: {len(self.active_pairs)}")
            
        except Exception as e:
            logger.error(f"❌ [MULTI-CURRENCY] Error initializing engine: {e}")
            raise
    
    async def discover_all_currencies(self):
        """Dynamically discover all available currencies across all exchanges without hardcoded lists"""
        try:
            logger.info("🔍 [CURRENCY-DISCOVERY] Scanning all exchanges for available currencies...")

            all_currencies = set()
            exchange_currency_data = {}

            for exchange_name, client in self.exchange_clients.items():
                try:
                    exchange_currencies = set()

                    # Method 1: Get all balances from this exchange with timeout
                    if hasattr(client, 'get_all_available_balances'):
                        try:
                            balances = await asyncio.wait_for(
                                client.get_all_available_balances(),
                                timeout=15.0
                            )
                            balance_currencies = set(balances.keys())
                            exchange_currencies.update(balance_currencies)
                            all_currencies.update(balance_currencies)

                            # Store balances for this exchange
                            self.currency_balances[exchange_name] = balances

                            logger.info(f"🔍 [CURRENCY-DISCOVERY] {exchange_name}: {len(balance_currencies)} currencies from balances")
                        except asyncio.TimeoutError:
                            logger.warning(f"⚠️ [CURRENCY-DISCOVERY] {exchange_name}: Balance discovery timed out, using fallback")
                            # Fallback to basic currencies
                            fallback_currencies = {'USDT', 'BTC', 'ETH', 'SOL', 'ADA', 'DOT'}
                            exchange_currencies.update(fallback_currencies)
                            all_currencies.update(fallback_currencies)

                    # Method 2: Discover from trading symbols/markets with timeout
                    try:
                        market_currencies = await asyncio.wait_for(
                            self._discover_currencies_from_trading_symbols(client, exchange_name),
                            timeout=10.0
                        )
                        exchange_currencies.update(market_currencies)
                        all_currencies.update(market_currencies)
                    except asyncio.TimeoutError:
                        logger.warning(f"⚠️ [CURRENCY-DISCOVERY] {exchange_name}: Symbol discovery timed out")

                    # Method 3: Discover from ticker data with timeout
                    try:
                        ticker_currencies = await asyncio.wait_for(
                            self._discover_currencies_from_tickers(client, exchange_name),
                            timeout=10.0
                        )
                        exchange_currencies.update(ticker_currencies)
                        all_currencies.update(ticker_currencies)
                    except asyncio.TimeoutError:
                        logger.warning(f"⚠️ [CURRENCY-DISCOVERY] {exchange_name}: Ticker discovery timed out")

                    # Store exchange-specific currency data
                    exchange_currency_data[exchange_name] = {
                        'currencies': exchange_currencies,
                        'count': len(exchange_currencies),
                        'discovery_methods': ['balances', 'symbols', 'tickers']
                    }

                    logger.info(f"🔍 [CURRENCY-DISCOVERY] {exchange_name}: {len(exchange_currencies)} total currencies discovered")

                except Exception as e:
                    logger.warning(f"⚠️ [CURRENCY-DISCOVERY] Error discovering currencies on {exchange_name}: {e}")
                    continue

            # Filter and validate discovered currencies
            valid_currencies = self._filter_and_validate_currencies(all_currencies)

            # Categorize currencies
            categorized_currencies = self._categorize_currencies(valid_currencies)

            # Store results
            self.supported_currencies = valid_currencies
            self.exchange_currency_data = exchange_currency_data
            self.categorized_currencies = categorized_currencies

            # Log discovery results
            logger.info(f"🔍 [CURRENCY-DISCOVERY] Discovery Summary:")
            logger.info(f"  Total raw currencies found: {len(all_currencies)}")
            logger.info(f"  Valid currencies after filtering: {len(valid_currencies)}")
            logger.info(f"  Stablecoins: {len(categorized_currencies['stablecoins'])}")
            logger.info(f"  Major cryptocurrencies: {len(categorized_currencies['major_crypto'])}")
            logger.info(f"  Altcoins: {len(categorized_currencies['altcoins'])}")
            logger.info(f"  Other tokens: {len(categorized_currencies['other'])}")

            # Log top currencies by category
            for category, currencies in categorized_currencies.items():
                if currencies:
                    top_currencies = sorted(list(currencies))[:10]  # Show top 10
                    logger.info(f"  {category.title()}: {top_currencies}")

        except Exception as e:
            logger.error(f"❌ [CURRENCY-DISCOVERY] Error discovering currencies: {e}")
            # Fallback to minimal set
            self.supported_currencies = {'USDT', 'USD', 'BTC', 'ETH'}

    async def _discover_currencies_from_trading_symbols(self, client, exchange_name: str) -> set:
        """Discover currencies from trading symbols/markets"""
        try:
            currencies = set()

            # Try different methods to get trading symbols
            symbol_methods = ['get_symbols', 'get_markets', 'get_trading_pairs']

            for method_name in symbol_methods:
                if hasattr(client, method_name):
                    try:
                        method = getattr(client, method_name)
                        symbols_data = await method()

                        if isinstance(symbols_data, list):
                            symbols = symbols_data
                        elif isinstance(symbols_data, dict):
                            symbols = list(symbols_data.keys())
                        else:
                            continue

                        for symbol in symbols:
                            if isinstance(symbol, str) and symbol:
                                base, quote = self._extract_currencies_from_symbol(symbol)
                                if base and quote:
                                    currencies.add(base)
                                    currencies.add(quote)

                        logger.info(f"🔍 [SYMBOLS] {exchange_name}: extracted currencies from {len(symbols)} symbols via {method_name}")
                        break  # Success, no need to try other methods

                    except Exception as e:
                        logger.debug(f"Method {method_name} failed for {exchange_name}: {e}")
                        continue

            return currencies

        except Exception as e:
            logger.debug(f"Error discovering from symbols for {exchange_name}: {e}")
            return set()

    async def _discover_currencies_from_tickers(self, client, exchange_name: str) -> set:
        """Discover currencies from ticker/price data"""
        try:
            currencies = set()

            # Try different methods to get ticker data
            ticker_methods = ['get_tickers', 'get_ticker_24hr', 'get_all_tickers']

            for method_name in ticker_methods:
                if hasattr(client, method_name):
                    try:
                        method = getattr(client, method_name)
                        ticker_data = await method()

                        symbols = []
                        if isinstance(ticker_data, list):
                            symbols = [item.get('symbol', '') for item in ticker_data if isinstance(item, dict)]
                        elif isinstance(ticker_data, dict):
                            if 'symbol' in ticker_data:
                                symbols = [ticker_data['symbol']]
                            else:
                                symbols = list(ticker_data.keys())

                        for symbol in symbols:
                            if isinstance(symbol, str) and symbol:
                                base, quote = self._extract_currencies_from_symbol(symbol)
                                if base and quote:
                                    currencies.add(base)
                                    currencies.add(quote)

                        logger.info(f"🔍 [TICKERS] {exchange_name}: extracted currencies from {len(symbols)} tickers via {method_name}")
                        break  # Success, no need to try other methods

                    except Exception as e:
                        logger.debug(f"Method {method_name} failed for {exchange_name}: {e}")
                        continue

            return currencies

        except Exception as e:
            logger.debug(f"Error discovering from tickers for {exchange_name}: {e}")
            return set()

    def _filter_and_validate_currencies(self, raw_currencies: set) -> set:
        """Filter and validate discovered currencies"""
        try:
            valid_currencies = set()

            for currency in raw_currencies:
                if (currency and
                    isinstance(currency, str) and
                    len(currency) >= 2 and
                    len(currency) <= 10 and
                    currency.isalpha() and
                    currency.isupper()):
                    valid_currencies.add(currency)

            # Remove obvious invalid currencies
            invalid_patterns = {'TEST', 'DEMO', 'NULL', 'NONE', 'UNKNOWN'}
            valid_currencies = valid_currencies - invalid_patterns

            return valid_currencies

        except Exception as e:
            logger.error(f"❌ [FILTER] Error filtering currencies: {e}")
            return set()

    def _categorize_currencies(self, currencies: set) -> Dict[str, set]:
        """Categorize currencies into different types"""
        try:
            # Define currency categories (dynamically expandable)
            stablecoins = set()
            major_crypto = set()
            altcoins = set()
            other = set()

            # Known patterns for categorization
            stablecoin_patterns = {'USD', 'USDT', 'USDC', 'DAI', 'BUSD', 'TUSD', 'FRAX', 'LUSD'}
            major_crypto_patterns = {'BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'DOT', 'AVAX', 'MATIC', 'LINK', 'UNI'}

            for currency in currencies:
                if currency in stablecoin_patterns or 'USD' in currency:
                    stablecoins.add(currency)
                elif currency in major_crypto_patterns:
                    major_crypto.add(currency)
                elif len(currency) <= 5:  # Likely established altcoins
                    altcoins.add(currency)
                else:
                    other.add(currency)

            return {
                'stablecoins': stablecoins,
                'major_crypto': major_crypto,
                'altcoins': altcoins,
                'other': other
            }

        except Exception as e:
            logger.error(f"❌ [CATEGORIZE] Error categorizing currencies: {e}")
            return {'stablecoins': set(), 'major_crypto': set(), 'altcoins': set(), 'other': set()}

    def _find_trading_pair(self, exchange_pairs: dict, base_currency: str, quote_currency: str) -> str:
        """Find trading pair symbol for given base and quote currencies (bidirectional)"""
        try:
            # Try direct format: BASEQUOTE
            direct_symbol = f"{base_currency}{quote_currency}"
            if direct_symbol in exchange_pairs:
                return direct_symbol

            # Try reverse format: QUOTEBASE
            reverse_symbol = f"{quote_currency}{base_currency}"
            if reverse_symbol in exchange_pairs:
                return reverse_symbol

            # Try with separators
            separator_formats = [
                f"{base_currency}-{quote_currency}",
                f"{quote_currency}-{base_currency}",
                f"{base_currency}/{quote_currency}",
                f"{quote_currency}/{base_currency}",
                f"{base_currency}_{quote_currency}",
                f"{quote_currency}_{base_currency}"
            ]

            for symbol_format in separator_formats:
                if symbol_format in exchange_pairs:
                    return symbol_format

            return None

        except Exception as e:
            logger.debug(f"Error finding trading pair for {base_currency}/{quote_currency}: {e}")
            return None

    async def _generate_valid_bybit_symbols(self, client, exchange_name: str) -> List[str]:
        """Generate ALL valid trading pairs using real exchange data - NO ARTIFICIAL RESTRICTIONS"""
        try:
            logger.info(f"🔍 [REAL-EXCHANGE-DATA] Discovering ALL valid trading pairs from {exchange_name} API...")

            # Get ALL available trading pairs directly from the exchange
            if hasattr(client, 'get_all_trading_pairs'):
                trading_pairs_data = client.get_all_trading_pairs()
                if trading_pairs_data and 'trading_pairs' in trading_pairs_data:
                    exchange_pairs = trading_pairs_data['trading_pairs']
                    valid_symbols = []

                    for symbol, pair_info in exchange_pairs.items():
                        status = pair_info.get('status', '').lower()
                        # Only include active trading pairs
                        if status in ['trading', 'active']:
                            valid_symbols.append(symbol)

                    logger.info(f"🔍 [REAL-EXCHANGE-DATA] Found {len(valid_symbols)} active trading pairs from {exchange_name}")
                    return valid_symbols

            # Fallback: Use market discovery system for Bybit
            if hasattr(client, 'currency_manager') and hasattr(client.currency_manager, 'market_discovery'):
                instruments = client.currency_manager.market_discovery.get_all_instruments("spot")
                valid_symbols = []

                for instrument in instruments:
                    symbol = instrument.get("symbol", "")
                    status = instrument.get("status", "").lower()

                    # Only include active trading symbols
                    if status in ["trading", "active"] and symbol:
                        valid_symbols.append(symbol)

                logger.info(f"🔍 [REAL-EXCHANGE-DATA] Found {len(valid_symbols)} active instruments from {exchange_name}")
                return valid_symbols

            # Last resort: Generate comprehensive pairs and validate with exchange
            logger.warning(f"⚠️ [FALLBACK] Using comprehensive pair generation for {exchange_name}")
            return await self._generate_comprehensive_trading_pairs(client, exchange_name)

        except Exception as e:
            logger.error(f"❌ [REAL-EXCHANGE-DATA] Error getting trading pairs from {exchange_name}: {e}")
            # Emergency fallback
            return await self._generate_comprehensive_trading_pairs(client, exchange_name)

    async def _generate_comprehensive_trading_pairs(self, client, exchange_name: str) -> List[str]:
        """Generate INTELLIGENT trading pairs focusing on detected balances and common patterns"""
        try:
            valid_symbols = []

            # Get currencies from detected balances (BTC, SOL, ADA, USDT, DOT)
            balance_currencies = list(self.currency_balances.get(exchange_name, {}).keys()) if hasattr(self, 'currency_balances') else []

            # Priority currencies based on detected balances and major cryptocurrencies
            priority_currencies = balance_currencies if balance_currencies else ['BTC', 'ETH', 'SOL', 'ADA', 'DOT']

            # Primary quote currencies (USDT is most common on Bybit)
            quote_currencies = ['USDT', 'USD', 'BTC', 'ETH']

            logger.info(f"🔍 [INTELLIGENT-PAIRS] Generating pairs for {len(priority_currencies)} priority currencies...")

            # Generate ONLY sensible trading pairs (base/quote order matters!)
            for base_currency in priority_currencies:
                if base_currency in ['USDT', 'USD']:  # Skip stablecoins as base
                    continue

                for quote_currency in quote_currencies:
                    if base_currency == quote_currency:
                        continue

                    # Generate symbol in correct order (e.g., BTCUSDT, not USDTBTC)
                    symbol = f"{base_currency}{quote_currency}"

                    try:
                        # Validate with real exchange API
                        if hasattr(client, '_is_valid_bybit_symbol'):
                            if client._is_valid_bybit_symbol(symbol):
                                if symbol not in valid_symbols:
                                    valid_symbols.append(symbol)
                                    logger.debug(f"✅ [VALIDATED] {symbol} confirmed by exchange API")
                    except Exception:
                        continue

            # Add some common major pairs if not already included
            common_pairs = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']
            for symbol in common_pairs:
                if symbol not in valid_symbols:
                    try:
                        if hasattr(client, '_is_valid_bybit_symbol'):
                            if client._is_valid_bybit_symbol(symbol):
                                valid_symbols.append(symbol)
                                logger.debug(f"✅ [COMMON] {symbol} added as common pair")
                    except Exception:
                        continue

            logger.info(f"🔍 [INTELLIGENT-PAIRS] Generated {len(valid_symbols)} intelligent trading pairs")
            return valid_symbols

        except Exception as e:
            logger.error(f"❌ [COMPREHENSIVE] Error in comprehensive pair generation: {e}")
            # Absolute fallback
            return ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']

    async def _validate_symbol_exists(self, client, symbol: str) -> bool:
        """Validate symbol using REAL exchange API data only"""
        try:
            # Always use the client's real exchange API validation
            if hasattr(client, '_is_valid_bybit_symbol'):
                is_valid = client._is_valid_bybit_symbol(symbol)
                logger.debug(f"🔍 [REAL-API-VALIDATION] {symbol}: {'VALID' if is_valid else 'INVALID'} (confirmed by exchange)")
                return is_valid

            # If client doesn't have API validation, assume valid and let the exchange reject it
            logger.warning(f"⚠️ [NO-API-VALIDATION] Client missing _is_valid_bybit_symbol method, assuming {symbol} is valid")
            return True

        except Exception as e:
            logger.debug(f"❌ [SYMBOL-VALIDATE] Error validating {symbol}: {e}")
            # On error, assume valid and let exchange API handle rejection
            return True

    def _basic_bybit_symbol_validation(self, symbol: str) -> bool:
        """REMOVED: No longer using artificial validation - delegating to real exchange API"""
        # This method is deprecated - all validation now uses real exchange data
        # via _is_valid_bybit_symbol() which queries the actual Bybit API
        logger.warning(f"⚠️ [DEPRECATED] _basic_bybit_symbol_validation called for {symbol} - use real exchange API instead")
        return True  # Allow all symbols to be validated by real exchange API

    def _parse_trading_pair(self, symbol: str) -> tuple:
        """Parse trading pair into base and quote currencies - UNIVERSAL PARSER"""
        try:
            # Handle different formats: BTC-USD, BTCUSDT, BTC/USD
            if '-' in symbol:
                parts = symbol.split('-')
                if len(parts) == 2:
                    return parts[0], parts[1]
            elif '/' in symbol:
                parts = symbol.split('/')
                if len(parts) == 2:
                    return parts[0], parts[1]
            else:
                # For formats like BTCUSDT, try to parse
                # Common quote currencies in order of priority
                quote_currencies = ['USDT', 'USDC', 'BTC', 'ETH', 'USD', 'EUR', 'GBP', 'JPY']

                for quote in quote_currencies:
                    if symbol.endswith(quote):
                        base = symbol[:-len(quote)]
                        if base:  # Ensure base currency is not empty
                            return base, quote

            # Fallback: assume last 3-4 characters are quote currency
            if len(symbol) > 4:
                return symbol[:-4], symbol[-4:]
            elif len(symbol) > 3:
                return symbol[:-3], symbol[-3:]
            else:
                return symbol, "UNKNOWN"

        except Exception as e:
            logger.debug(f"❌ [PARSE] Error parsing trading pair {symbol}: {e}")
            return symbol, "UNKNOWN"
    
    async def discover_all_trading_pairs(self):
        """Discover valid trading pairs using proper Bybit symbol validation"""
        try:
            logger.info("🔍 [PAIR-DISCOVERY] Discovering valid trading pairs...")

            discovered_pairs = {}

            for exchange_name, client in self.exchange_clients.items():
                try:
                    exchange_pairs = {}

                    # CRITICAL FIX: Use proper Bybit symbol generation with timeout
                    try:
                        valid_symbols = await asyncio.wait_for(
                            self._generate_valid_bybit_symbols(client, exchange_name),
                            timeout=20.0
                        )
                    except asyncio.TimeoutError:
                        logger.warning(f"⚠️ [PAIR-DISCOVERY] {exchange_name}: Symbol generation timed out, using fallback")
                        # Fallback to basic trading pairs
                        valid_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']

                    logger.info(f"🔍 [PAIR-DISCOVERY] Generated {len(valid_symbols)} valid symbols for {exchange_name}")

                    # CRITICAL FIX: Process symbols in batches and add comprehensive logging
                    processed_count = 0
                    validated_count = 0

                    for symbol in valid_symbols:
                        try:
                            processed_count += 1

                            # Parse the symbol to get base and quote currencies
                            base, quote = self._parse_trading_pair(symbol)

                            if not base or not quote or base == "UNKNOWN" or quote == "UNKNOWN":
                                logger.debug(f"❌ [PAIR-DISCOVERY] Failed to parse {symbol}: base={base}, quote={quote}")
                                continue

                            # CRITICAL FIX: Skip validation for now and create pairs directly from exchange data
                            # Since we already got these symbols from the exchange API, they should be valid

                            # Create currency pair object
                            pair = CurrencyPair(
                                base=base,
                                quote=quote,
                                symbol=symbol,
                                exchange=exchange_name,
                                min_order_value=Decimal('5.0'),  # Default minimum
                                tick_size=Decimal('0.00000001'),
                                lot_size=Decimal('0.00000001'),
                                is_active=True,
                                liquidity_score=1.0
                            )

                            exchange_pairs[symbol] = pair
                            validated_count += 1

                            if validated_count <= 10:  # Log first 10 for debugging
                                logger.info(f"✅ [PAIR-DISCOVERY] Added {symbol} ({base}/{quote}) on {exchange_name}")

                        except Exception as e:
                            logger.warning(f"❌ [PAIR-DISCOVERY] Error processing {symbol}: {e}")
                            continue

                    logger.info(f"🔍 [PAIR-DISCOVERY] Processed {processed_count} symbols, validated {validated_count} pairs")
                    
                    discovered_pairs[exchange_name] = exchange_pairs
                    logger.info(f"🔍 [PAIR-DISCOVERY] {exchange_name}: {len(exchange_pairs)} trading pairs")
                    
                except Exception as e:
                    logger.warning(f"⚠️ [PAIR-DISCOVERY] Error discovering pairs on {exchange_name}: {e}")
                    continue
            
            self.active_pairs = discovered_pairs
            
            # Calculate total unique pairs
            all_symbols = set()
            for exchange_pairs in discovered_pairs.values():
                all_symbols.update(exchange_pairs.keys())
            
            logger.info(f"🔍 [PAIR-DISCOVERY] Total unique trading pairs: {len(all_symbols)}")
            
        except Exception as e:
            logger.error(f"❌ [PAIR-DISCOVERY] Error discovering trading pairs: {e}")
    
    async def find_trading_opportunities(self) -> List[TradingOpportunity]:
        """Find trading opportunities across all currencies and pairs with TIMEOUT PROTECTION"""
        import time
        import asyncio

        # TIMEOUT CONFIGURATION to prevent hanging
        TOTAL_SCAN_TIMEOUT = 120  # 2 minutes maximum for entire scan
        STRATEGY_TIMEOUT = 30     # 30 seconds maximum per strategy

        start_time = time.time()
        opportunities = []

        try:
            logger.info("🎯 [OPPORTUNITY-SCAN] Scanning for multi-currency trading opportunities with enhanced data analysis...")

            # ENHANCED: Gather market intelligence before opportunity generation
            market_intelligence = await self._gather_market_intelligence()

            # Strategy 1: Direct currency trading based on balances (PRIORITY - ALWAYS EXECUTE)
            try:
                logger.info("🎯 [STRATEGY-1] Finding direct trading opportunities with market intelligence...")
                direct_start = time.time()
                direct_opportunities = await asyncio.wait_for(
                    self._find_direct_trading_opportunities_enhanced(market_intelligence),
                    timeout=STRATEGY_TIMEOUT
                )
                opportunities.extend(direct_opportunities)
                logger.info(f"🎯 [STRATEGY-1] Found {len(direct_opportunities)} direct opportunities in {time.time() - direct_start:.2f}s")
            except asyncio.TimeoutError:
                logger.warning(f"⏰ [STRATEGY-1] Direct trading scan timed out after {STRATEGY_TIMEOUT}s")
            except Exception as e:
                logger.warning(f"⚠️ [STRATEGY-1] Error finding direct opportunities: {e}")
                # Fallback to basic direct opportunities
                try:
                    direct_opportunities = await asyncio.wait_for(
                        self._find_direct_trading_opportunities(),
                        timeout=15
                    )
                    opportunities.extend(direct_opportunities)
                    logger.info(f"🎯 [STRATEGY-1-FALLBACK] Found {len(direct_opportunities)} fallback opportunities")
                except Exception as fallback_error:
                    logger.warning(f"⚠️ [STRATEGY-1-FALLBACK] Fallback also failed: {fallback_error}")

            # Check total timeout before continuing
            if time.time() - start_time > TOTAL_SCAN_TIMEOUT:
                logger.warning(f"⏰ [OPPORTUNITY-SCAN] Total timeout reached - returning {len(opportunities)} opportunities")
                return opportunities

            # Strategy 2: Cross-currency arbitrage (OPTIONAL - CAN SKIP IF TIMEOUT)
            try:
                logger.info("🎯 [STRATEGY-2] Finding cross-currency arbitrage...")
                arbitrage_start = time.time()
                arbitrage_opportunities = await asyncio.wait_for(
                    self._find_arbitrage_opportunities(),
                    timeout=STRATEGY_TIMEOUT
                )
                opportunities.extend(arbitrage_opportunities)
                logger.info(f"🎯 [STRATEGY-2] Found {len(arbitrage_opportunities)} arbitrage opportunities in {time.time() - arbitrage_start:.2f}s")
            except asyncio.TimeoutError:
                logger.warning(f"⏰ [STRATEGY-2] Arbitrage scan timed out after {STRATEGY_TIMEOUT}s - skipping")
            except Exception as e:
                logger.warning(f"⚠️ [STRATEGY-2] Error finding arbitrage opportunities: {e}")

            # Check total timeout before continuing
            if time.time() - start_time > TOTAL_SCAN_TIMEOUT:
                logger.warning(f"⏰ [OPPORTUNITY-SCAN] Total timeout reached - returning {len(opportunities)} opportunities")
                return opportunities

            # Strategy 3: Triangular arbitrage within exchanges (OPTIONAL - CAN SKIP IF TIMEOUT)
            try:
                logger.info("🎯 [STRATEGY-3] Finding triangular arbitrage...")
                triangular_start = time.time()
                triangular_opportunities = await asyncio.wait_for(
                    self._find_triangular_arbitrage(),
                    timeout=STRATEGY_TIMEOUT
                )
                opportunities.extend(triangular_opportunities)
                logger.info(f"🎯 [STRATEGY-3] Found {len(triangular_opportunities)} triangular opportunities in {time.time() - triangular_start:.2f}s")
            except asyncio.TimeoutError:
                logger.warning(f"⏰ [STRATEGY-3] Triangular arbitrage scan timed out after {STRATEGY_TIMEOUT}s - skipping")
            except Exception as e:
                logger.warning(f"⚠️ [STRATEGY-3] Error finding triangular arbitrage: {e}")

            # Check total timeout before continuing
            if time.time() - start_time > TOTAL_SCAN_TIMEOUT:
                logger.warning(f"⏰ [OPPORTUNITY-SCAN] Total timeout reached - returning {len(opportunities)} opportunities")
                return opportunities

            # Strategy 4: Balance rebalancing opportunities (OPTIONAL)
            try:
                logger.info("🎯 [STRATEGY-4] Finding rebalancing opportunities...")
                rebalancing_start = time.time()
                rebalancing_opportunities = await asyncio.wait_for(
                    self._find_rebalancing_opportunities(),
                    timeout=STRATEGY_TIMEOUT
                )
                opportunities.extend(rebalancing_opportunities)
                logger.info(f"🎯 [STRATEGY-4] Found {len(rebalancing_opportunities)} rebalancing opportunities in {time.time() - rebalancing_start:.2f}s")
            except asyncio.TimeoutError:
                logger.warning(f"⏰ [STRATEGY-4] Rebalancing scan timed out after {STRATEGY_TIMEOUT}s - skipping")
            except Exception as e:
                logger.warning(f"⚠️ [STRATEGY-4] Error finding rebalancing opportunities: {e}")

            # Strategy 5: Additional arbitrage engine opportunities (OPTIONAL)
            if self.arbitrage_engine and time.time() - start_time < TOTAL_SCAN_TIMEOUT:
                try:
                    logger.info("🎯 [STRATEGY-5] Finding additional arbitrage opportunities...")
                    additional_arbitrage = await asyncio.wait_for(
                        self._find_arbitrage_opportunities(),
                        timeout=STRATEGY_TIMEOUT
                    )
                    opportunities.extend(additional_arbitrage)
                except asyncio.TimeoutError:
                    logger.warning(f"⏰ [STRATEGY-5] Additional arbitrage scan timed out - skipping")
                except Exception as e:
                    logger.warning(f"⚠️ [STRATEGY-5] Error finding additional arbitrage: {e}")

            # Strategy 6: Portfolio rebalancing opportunities (OPTIONAL)
            if self.portfolio_rebalancer and time.time() - start_time < TOTAL_SCAN_TIMEOUT:
                try:
                    logger.info("🎯 [STRATEGY-6] Finding portfolio rebalancing opportunities...")
                    portfolio_rebalancing = await asyncio.wait_for(
                        self._find_portfolio_rebalancing_opportunities(),
                        timeout=STRATEGY_TIMEOUT
                    )
                    opportunities.extend(portfolio_rebalancing)
                except asyncio.TimeoutError:
                    logger.warning(f"⏰ [STRATEGY-6] Portfolio rebalancing scan timed out - skipping")
                except Exception as e:
                    logger.warning(f"⚠️ [STRATEGY-6] Error finding portfolio rebalancing: {e}")

            # Sort by execution priority and expected profit
            opportunities.sort(key=lambda x: (x.execution_priority, -float(x.expected_profit)))

            total_time = time.time() - start_time
            logger.info(f"🎯 [OPPORTUNITY-SCAN] Found {len(opportunities)} trading opportunities in {total_time:.2f}s")

            # APPLY DIVERSIFICATION FILTERS BEFORE CURRENCY OPTIMIZATION
            if opportunities:
                original_count = len(opportunities)

                # ENHANCED: Filter opportunities with intelligent diversification
                filtered_opportunities = []
                pair_frequency = {}  # Track how often each pair appears

                # Count pair frequency in opportunities
                for opp in opportunities:
                    pair_symbol = f"{opp.pair.base}{opp.pair.quote}"
                    pair_frequency[pair_symbol] = pair_frequency.get(pair_symbol, 0) + 1

                # Apply diversification filters
                for opp in opportunities:
                    pair_symbol = f"{opp.pair.base}{opp.pair.quote}"

                    # Check if pair is on cooldown
                    if self._is_pair_on_cooldown(pair_symbol):
                        logger.debug(f"🔄 [PAIR-FILTER] Skipping {pair_symbol} - on cooldown")
                        continue

                    # Check if strategy is on cooldown
                    if self._is_strategy_on_cooldown(opp.strategy):
                        logger.debug(f"🔄 [STRATEGY-FILTER] Skipping {opp.strategy} - on cooldown")
                        continue

                    # ENHANCED: Check if we should force diversification
                    if self._should_force_diversification(pair_symbol):
                        logger.info(f"🔄 [DIVERSIFICATION-FILTER] Skipping {pair_symbol} - forcing diversification")
                        continue

                    # NEW: Limit opportunities per pair to prevent concentration
                    current_pair_count = len([o for o in filtered_opportunities if f"{o.pair.base}{o.pair.quote}" == pair_symbol])
                    if current_pair_count >= 2:  # Max 2 opportunities per pair
                        logger.debug(f"🔄 [CONCENTRATION-FILTER] Skipping {pair_symbol} - too many opportunities for this pair")
                        continue

                    # NEW: Profit-first validation (minimum 0.5% profit after fees)
                    if not self._validate_profit_potential(opp):
                        logger.debug(f"🔄 [PROFIT-FILTER] Skipping {pair_symbol} - insufficient profit potential")
                        continue

                    # NEW: Smart trade validation (prevent low-gain trades)
                    if not self._validate_smart_trade_criteria(opp):
                        logger.debug(f"🔄 [SMART-FILTER] Skipping {pair_symbol} - fails smart trade criteria")
                        continue

                    filtered_opportunities.append(opp)

                opportunities = filtered_opportunities
                logger.info(f"🔄 [DIVERSIFICATION] Filtered {original_count} -> {len(opportunities)} opportunities after diversification")

            # CURRENCY OPTIMIZATION: Log optimal currency selection details
            if opportunities:
                logger.info(f"💰 [CURRENCY-OPTIMIZATION] Analyzing {len(opportunities)} opportunities for optimal selection")

                # Group by currency pairs and show profitability
                currency_profits = {}
                for opp in opportunities:
                    pair = f"{opp.pair.base}/{opp.pair.quote}"
                    if pair not in currency_profits:
                        currency_profits[pair] = []
                    currency_profits[pair].append(opp.expected_profit)

                # Log top profitable pairs
                sorted_pairs = sorted(currency_profits.items(),
                                    key=lambda x: max(x[1]), reverse=True)[:5]

                logger.info(f"💰 [TOP-PROFITABLE-PAIRS] Most profitable currency pairs:")
                for pair, profits in sorted_pairs:
                    max_profit = max(profits)
                    avg_profit = sum(profits) / len(profits)
                    logger.info(f"💰 [CURRENCY-PAIR] {pair}: Max: {max_profit:.4f}, Avg: {avg_profit:.4f}, Count: {len(profits)}")

            # BALANCE UTILIZATION: Show aggressive micro-trading strategy
            logger.info(f"💰 [BALANCE-UTILIZATION] Strategy: 80-90% aggressive micro-trading with ≥0.60 confidence")

            # DIVERSIFICATION STATUS LOGGING
            logger.info(f"🔄 [DIVERSIFICATION-STATUS] Active pair cooldowns: {len(self.pair_cooldowns)}")
            logger.info(f"🔄 [DIVERSIFICATION-STATUS] Active strategy cooldowns: {len(self.strategy_cooldowns)}")
            logger.info(f"🔄 [DIVERSIFICATION-STATUS] Recent traded pairs: {self.last_traded_pairs[:5]}")

            # Show cooldown details
            if self.pair_cooldowns:
                current_time = time.time()
                for pair, cooldown_end in list(self.pair_cooldowns.items())[:3]:  # Show top 3
                    remaining = max(0, cooldown_end - current_time)
                    logger.info(f"🔄 [PAIR-COOLDOWN] {pair}: {remaining:.0f}s remaining")

            if self.strategy_cooldowns:
                current_time = time.time()
                for strategy, cooldown_end in self.strategy_cooldowns.items():
                    remaining = max(0, cooldown_end - current_time)
                    logger.info(f"🔄 [STRATEGY-COOLDOWN] {strategy}: {remaining:.0f}s remaining")

            return opportunities

        except Exception as e:
            logger.error(f"❌ [OPPORTUNITY-SCAN] Error finding opportunities: {e}")
            return opportunities  # Return any opportunities found so far

    async def scan_for_trading_opportunities(self) -> List[TradingOpportunity]:
        """Alias for find_trading_opportunities to maintain compatibility"""
        return await self.find_trading_opportunities()
    
    async def _find_direct_trading_opportunities(self) -> List[TradingOpportunity]:
        """Find direct trading opportunities using available balances - ENHANCED LOGIC WITH DIVERSIFICATION"""
        opportunities = []

        try:
            logger.info("🎯 [DIRECT-TRADING] Scanning balances for trading opportunities with diversification...")

            for exchange_name, balances in self.currency_balances.items():
                logger.info(f"🎯 [DIRECT-TRADING] Checking {exchange_name}: {len(balances)} currencies")

                # STRATEGIC ENHANCEMENT: Add profit-driven diversified BUY opportunities
                usdt_balance = balances.get('USDT', 0)
                if usdt_balance >= 10:  # Minimum $10 USDT for BUY orders
                    logger.info(f"🎯 [STRATEGIC-DIVERSIFIED-BUY] USDT balance ${usdt_balance:.2f} - creating strategic opportunities")

                    # Get strategically selected target currencies based on profit potential
                    all_candidates = list(self.discovered_currencies.get(exchange_name, set()))
                    if not all_candidates:
                        all_candidates = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'AVAX', 'UNI', 'MATIC', 'ALGO']

                    # Remove stablecoins
                    stablecoins = {'USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'FDUSD'}
                    candidates = [curr for curr in all_candidates if curr not in stablecoins]

                    # Strategic selection based on profit potential
                    strategic_targets = await self._select_strategic_profit_targets(candidates, exchange_name, balances)

                    # Apply diversification rules
                    diversified_targets = await self._apply_strategic_diversification_rules(
                        strategic_targets, set(balances.keys()), exchange_name
                    )

                    # Create opportunities for top strategic targets
                    created_opportunities = 0
                    for target_currency in diversified_targets[:8]:  # Top 8 strategic targets
                        symbol = f"{target_currency}USDT"

                        # Skip if pair is on cooldown
                        if self._is_pair_on_cooldown(symbol):
                            logger.debug(f"🔄 [STRATEGIC-BUY] Skipping {symbol} - on cooldown")
                            continue

                        # Create strategic BUY opportunity
                        buy_opportunity = await self._create_diversified_buy_opportunity(
                            exchange_name, target_currency, usdt_balance
                        )

                        if buy_opportunity:
                            opportunities.append(buy_opportunity)
                            created_opportunities += 1
                            logger.info(f"🎯 [STRATEGIC-BUY] Created strategic opportunity: {symbol}")

                            # Limit opportunities to prevent over-diversification
                            if created_opportunities >= 5:
                                break

                    logger.info(f"🎯 [STRATEGIC-DIVERSIFIED-BUY] Created {created_opportunities} strategic opportunities")

                # EXISTING LOGIC: Create SELL opportunities for held currencies
                for currency, balance in balances.items():
                    if balance <= 0:
                        continue

                    logger.debug(f"🎯 [DIRECT-TRADING] Evaluating {currency}: {balance}")

                    # CRITICAL FIX: Create opportunities for all non-USDT currencies
                    if currency != 'USDT' and currency != 'USD':
                        # Opportunity to sell crypto for USDT
                        symbol = f"{currency}USDT"

                        try:
                            # Check if this symbol exists in our active pairs
                            pair = None
                            if exchange_name in self.active_pairs and symbol in self.active_pairs[exchange_name]:
                                pair = self.active_pairs[exchange_name][symbol]
                            else:
                                # Create a temporary pair if not found
                                pair = CurrencyPair(
                                    base=currency,
                                    quote='USDT',
                                    symbol=symbol,
                                    exchange=exchange_name,
                                    min_order_value=Decimal('5.0'),
                                    tick_size=Decimal('0.00000001'),
                                    lot_size=Decimal('0.00000001'),
                                    is_active=True,
                                    liquidity_score=1.0
                                )

                            # Get current price
                            client = self.exchange_clients[exchange_name]
                            price = client.get_price(symbol)

                            if price and float(price) > 0:
                                # INTELLIGENT BALANCE-BASED SELLING LOGIC
                                # Check USDT balance to determine sell percentage
                                usdt_balance = balances.get('USDT', 0)

                                # Dynamic sell percentage based on USDT balance
                                if usdt_balance < 10:  # Low USDT - sell more crypto aggressively
                                    sell_percentage = 0.90  # 90% for aggressive USDT accumulation
                                    priority = 1  # High priority
                                    confidence = 0.85  # High confidence for needed USDT
                                elif usdt_balance < 30:  # Medium USDT - moderate selling
                                    sell_percentage = 0.75  # 75% for balanced approach
                                    priority = 2  # Medium priority
                                    confidence = 0.80
                                elif usdt_balance < 60:  # Good USDT - conservative selling
                                    sell_percentage = 0.60  # 60% to maintain crypto holdings
                                    priority = 3  # Lower priority
                                    confidence = 0.75
                                else:  # High USDT - minimal selling
                                    sell_percentage = 0.40  # 40% to preserve crypto diversity
                                    priority = 4  # Lowest priority
                                    confidence = 0.70

                                # Calculate tradeable amount with dynamic percentage
                                tradeable_amount = Decimal(str(balance)) * Decimal(str(sell_percentage))
                                usd_value = tradeable_amount * Decimal(str(price))

                                # Check if meets minimum order value
                                if usd_value >= Decimal('5.0'):  # $5 minimum
                                    opportunity = TradingOpportunity(
                                        pair=pair,
                                        side='sell',
                                        amount=tradeable_amount,
                                        price=Decimal(str(price)),
                                        expected_profit=Decimal('0'),
                                        confidence=confidence,
                                        strategy='direct_currency_trading',
                                        risk_score=0.3,
                                        execution_priority=priority
                                    )
                                    opportunities.append(opportunity)
                                    logger.info(f"✅ [DIRECT-TRADING] SELL opportunity: {currency} -> USDT (${usd_value:.2f}, {sell_percentage*100:.0f}% of holdings)")
                                    logger.info(f"💰 [BALANCE-LOGIC] USDT: ${usdt_balance:.2f} -> Sell {sell_percentage*100:.0f}% of {currency} (Priority: {priority})")
                                else:
                                    logger.debug(f"❌ [DIRECT-TRADING] {symbol} value ${usd_value:.2f} below minimum")
                            else:
                                logger.debug(f"❌ [DIRECT-TRADING] No price for {symbol}")

                        except Exception as e:
                            logger.debug(f"❌ [DIRECT-TRADING] Error evaluating {symbol}: {e}")
                            continue

                    # CRITICAL FIX: Create opportunities to buy crypto with USDT
                    elif currency == 'USDT' and balance >= 10.0:  # $10 minimum USDT
                        # ENHANCED: Dynamic target currency selection with diversification
                        target_currencies = await self._get_diversified_target_currencies(exchange_name, balances)

                        for target_currency in target_currencies:
                            symbol = f"{target_currency}USDT"

                            try:
                                # Check if this symbol exists
                                pair = None
                                if exchange_name in self.active_pairs and symbol in self.active_pairs[exchange_name]:
                                    pair = self.active_pairs[exchange_name][symbol]
                                else:
                                    # Create a temporary pair
                                    pair = CurrencyPair(
                                        base=target_currency,
                                        quote='USDT',
                                        symbol=symbol,
                                        exchange=exchange_name,
                                        min_order_value=Decimal('5.0'),
                                        tick_size=Decimal('0.00000001'),
                                        lot_size=Decimal('0.00000001'),
                                        is_active=True,
                                        liquidity_score=1.0
                                    )

                                # Get current price
                                client = self.exchange_clients[exchange_name]
                                price = client.get_price(symbol)

                                if price and float(price) > 0:
                                    # ENHANCED DIVERSIFICATION LOGIC FOR BUY OPPORTUNITIES
                                    # Calculate how many crypto currencies we already have
                                    crypto_currencies = [curr for curr in balances.keys() if curr != 'USDT' and balances[curr] > 0]
                                    num_crypto_holdings = len(crypto_currencies)

                                    # Dynamic allocation based on diversification needs
                                    if num_crypto_holdings == 0:  # No crypto - aggressive buying
                                        allocation_percentage = 0.30  # 30% per currency for initial diversification
                                        priority = 1  # High priority
                                        confidence = 0.85
                                    elif num_crypto_holdings <= 2:  # Few cryptos - moderate buying
                                        allocation_percentage = 0.25  # 25% per currency
                                        priority = 2  # Medium priority
                                        confidence = 0.80
                                    elif num_crypto_holdings <= 4:  # Good diversification - conservative buying
                                        allocation_percentage = 0.15  # 15% per currency
                                        priority = 3  # Lower priority
                                        confidence = 0.75
                                    else:  # Well diversified - minimal buying
                                        allocation_percentage = 0.10  # 10% per currency
                                        priority = 4  # Lowest priority
                                        confidence = 0.70

                                    # Check if we already have this currency (reduce allocation if we do)
                                    if target_currency in crypto_currencies:
                                        allocation_percentage *= 0.7  # Reduce by 30% if we already have it
                                        priority += 1  # Lower priority for existing holdings
                                        logger.info(f"🔄 [DIVERSIFICATION] Reducing {target_currency} allocation - already held")

                                    usdt_amount = Decimal(str(balance)) * Decimal(str(allocation_percentage))
                                    crypto_amount = usdt_amount / Decimal(str(price))

                                    if usdt_amount >= Decimal('5.0'):  # $5 minimum
                                        opportunity = TradingOpportunity(
                                            pair=pair,
                                            side='buy',
                                            amount=crypto_amount,
                                            price=Decimal(str(price)),
                                            expected_profit=Decimal('0'),
                                            confidence=confidence,
                                            strategy='direct_currency_trading',
                                            risk_score=0.4,
                                            execution_priority=priority
                                        )
                                        opportunities.append(opportunity)
                                        logger.info(f"✅ [DIRECT-TRADING] BUY opportunity: USDT -> {target_currency} (${usdt_amount:.2f}, {allocation_percentage*100:.1f}% allocation)")
                                        logger.info(f"💰 [DIVERSIFICATION] Holdings: {num_crypto_holdings} cryptos, Priority: {priority}, Confidence: {confidence:.2f}")

                            except Exception as e:
                                logger.debug(f"❌ [DIRECT-TRADING] Error evaluating buy {symbol}: {e}")
                                continue

            logger.info(f"🎯 [DIRECT-TRADING] Found {len(opportunities)} direct trading opportunities")

        except Exception as e:
            logger.error(f"❌ [DIRECT-TRADING] Error finding direct opportunities: {e}")

        return opportunities

    async def _get_diversified_target_currencies(self, exchange_name: str, current_balances: Dict[str, float]) -> List[str]:
        """Get diversified target currencies for trading with intelligent rotation and cooldown awareness"""
        try:
            # Get all available currencies from the exchange
            all_currencies = list(self.supported_currencies) if self.supported_currencies else []

            # Remove stablecoins and current base currency
            excluded_currencies = {'USDT', 'USD', 'USDC', 'BUSD', 'DAI', 'TUSD', 'USDP'}
            available_currencies = [curr for curr in all_currencies if curr not in excluded_currencies]

            # If no dynamic discovery, use enhanced fallback list
            if not available_currencies:
                available_currencies = [
                    'BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'MATIC', 'LINK', 'UNI', 'AVAX', 'ATOM',
                    'NEAR', 'FTM', 'ALGO', 'XLM', 'VET', 'ICP', 'THETA', 'FIL', 'TRX', 'EOS'
                ]

            # ENHANCED: Always ensure we have a good variety of currencies
            if len(available_currencies) < 10:
                # Add more currencies to ensure good diversification
                additional_currencies = [
                    'LTC', 'BCH', 'XRP', 'DOGE', 'SHIB', 'PEPE', 'ARB', 'OP', 'MANA', 'SAND',
                    'CRV', 'COMP', 'AAVE', 'MKR', 'SNX', 'YFI', 'SUSHI', '1INCH', 'BAL', 'REN'
                ]
                for curr in additional_currencies:
                    if curr not in available_currencies:
                        available_currencies.append(curr)
                        if len(available_currencies) >= 20:
                            break

            # Apply diversification logic
            diversified_currencies = []
            current_holdings = [curr for curr, bal in current_balances.items() if bal > 0 and curr != 'USDT']

            # Priority 1: Currencies we don't currently hold (for diversification)
            unowned_currencies = [curr for curr in available_currencies if curr not in current_holdings]

            # Priority 2: Major cryptocurrencies (always include for liquidity)
            major_currencies = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'MATIC']
            major_unowned = [curr for curr in major_currencies if curr in unowned_currencies]

            # Priority 3: Filter out currencies on cooldown
            current_time = time.time()
            non_cooldown_currencies = []

            for currency in available_currencies:
                pair_symbol = f"{currency}USDT"
                if not self._is_pair_on_cooldown(pair_symbol):
                    non_cooldown_currencies.append(currency)

            # Build diversified list with intelligent selection
            # 1. Add major currencies not on cooldown and not owned (highest priority)
            for curr in major_unowned:
                if curr in non_cooldown_currencies and len(diversified_currencies) < 8:
                    diversified_currencies.append(curr)

            # 2. Add other unowned currencies not on cooldown
            for curr in unowned_currencies:
                if curr not in diversified_currencies and curr in non_cooldown_currencies and len(diversified_currencies) < 12:
                    diversified_currencies.append(curr)

            # 3. Add owned currencies not on cooldown (for rebalancing)
            owned_non_cooldown = [curr for curr in current_holdings if curr in non_cooldown_currencies]
            for curr in owned_non_cooldown:
                if curr not in diversified_currencies and len(diversified_currencies) < 15:
                    diversified_currencies.append(curr)

            # 4. If still need more, add any available currencies not on cooldown
            for curr in non_cooldown_currencies:
                if curr not in diversified_currencies and len(diversified_currencies) < 20:
                    diversified_currencies.append(curr)

            # 5. Emergency fallback: if all currencies are on cooldown, use major currencies anyway
            if not diversified_currencies:
                logger.warning("🔄 [DIVERSIFICATION] All currencies on cooldown - using emergency fallback")
                diversified_currencies = major_currencies[:5]

            # Shuffle for additional randomization
            import random
            random.shuffle(diversified_currencies)

            logger.info(f"🎯 [DIVERSIFICATION] Selected {len(diversified_currencies)} target currencies for {exchange_name}")
            logger.info(f"🎯 [DIVERSIFICATION] Targets: {diversified_currencies[:10]}")  # Show first 10
            logger.info(f"🔄 [DIVERSIFICATION] Current holdings: {len(current_holdings)} currencies")
            logger.info(f"🔄 [DIVERSIFICATION] Cooldown pairs: {len(self.pair_cooldowns)}")

            return diversified_currencies

        except Exception as e:
            logger.error(f"❌ [DIVERSIFICATION] Error getting diversified currencies: {e}")
            # Emergency fallback
            return ['BTC', 'ETH', 'SOL', 'ADA', 'DOT']

    async def _select_strategic_profit_targets(self, candidate_currencies: List[str], exchange_name: str, current_balances: Dict[str, float]) -> List[str]:
        """Select currencies based on profit potential and strategic analysis"""
        try:
            # FIXED: Use simplified strategic analysis without neural dependencies
            # This avoids the missing LSTM processor import error
            strategic_targets = []

            # Analyze each candidate currency for profit potential using market-based analysis
            for currency in candidate_currencies:
                try:
                    symbol = f"{currency}USDT"

                    # Skip if on cooldown
                    if self._is_pair_on_cooldown(symbol):
                        continue

                    # Get market data for analysis
                    market_data = await self._get_market_data_for_analysis(symbol, exchange_name)

                    if not market_data:
                        continue

                    # Calculate strategic score using market-based analysis (no neural dependencies)
                    strategic_score = await self._calculate_market_based_strategic_score(
                        currency, market_data, current_balances
                    )

                    # Calculate profit potential from market momentum
                    momentum_24h = market_data.get('momentum_24h', 0.02)
                    volume_change = market_data.get('volume_change', 0.15)
                    profit_potential = (momentum_24h + volume_change) / 2

                    # Only include currencies with positive profit potential and good strategic score
                    if (profit_potential > 0.015 and  # >1.5% profit potential
                        strategic_score > 0.6):  # >60% strategic score

                        strategic_targets.append({
                            'currency': currency,
                            'profit_potential': profit_potential,
                            'strategic_score': strategic_score,
                            'confidence': min(0.9, strategic_score + 0.2),
                            'market_momentum': momentum_24h
                        })

                except Exception as e:
                    logger.debug(f"⚠️ [STRATEGIC-ANALYSIS] Error analyzing {currency}: {e}")
                    continue

            # Sort by combined profit potential and strategic score
            strategic_targets.sort(
                key=lambda x: (x['profit_potential'] * x['strategic_score'] * x['confidence']),
                reverse=True
            )

            # Extract currency names
            selected_currencies = [target['currency'] for target in strategic_targets]

            logger.info(f"🎯 [STRATEGIC-SELECTION] Analyzed {len(candidate_currencies)} currencies")
            logger.info(f"🎯 [STRATEGIC-SELECTION] Selected {len(selected_currencies)} profitable targets")

            if selected_currencies:
                top_target = strategic_targets[0]
                logger.info(f"🎯 [TOP-TARGET] {top_target['currency']}: {top_target['profit_potential']:.3f} profit, {top_target['strategic_score']:.3f} score")

            return selected_currencies

            strategic_targets = []

            # Analyze each candidate currency for profit potential
            for currency in candidate_currencies:
                try:
                    symbol = f"{currency}USDT"

                    # Skip if on cooldown
                    if self._is_pair_on_cooldown(symbol):
                        continue

                    # Get market data for analysis
                    market_data = await self._get_market_data_for_analysis(symbol, exchange_name)

                    if not market_data:
                        continue

                    # Predict profit potential
                    profit_prediction = await self.profit_predictor.predict_profit_with_time_optimization(
                        symbol=symbol,
                        features=market_data,
                        sentiment_data=None,  # Could be enhanced with sentiment
                        risk_data={'risk_score': 0.3}  # Conservative risk
                    )

                    # Calculate strategic score
                    strategic_score = await self._calculate_strategic_score(
                        currency, profit_prediction, market_data, current_balances
                    )

                    # Only include currencies with positive profit potential and good strategic score
                    if (profit_prediction.get('predicted_profit', 0) > 0.01 and  # >1% profit potential
                        strategic_score > 0.6):  # >60% strategic score

                        strategic_targets.append({
                            'currency': currency,
                            'profit_potential': profit_prediction.get('predicted_profit', 0),
                            'strategic_score': strategic_score,
                            'confidence': profit_prediction.get('confidence', 0.5),
                            'risk_adjusted_profit': profit_prediction.get('risk_adjusted_profit', 0)
                        })

                except Exception as e:
                    logger.debug(f"⚠️ [STRATEGIC-ANALYSIS] Error analyzing {currency}: {e}")
                    continue

            # Sort by combined profit potential and strategic score
            strategic_targets.sort(
                key=lambda x: (x['profit_potential'] * x['strategic_score'] * x['confidence']),
                reverse=True
            )

            # Extract currency names
            selected_currencies = [target['currency'] for target in strategic_targets]

            logger.info(f"🎯 [STRATEGIC-SELECTION] Analyzed {len(candidate_currencies)} currencies")
            logger.info(f"🎯 [STRATEGIC-SELECTION] Selected {len(selected_currencies)} profitable targets")

            if selected_currencies:
                top_target = strategic_targets[0]
                logger.info(f"🎯 [TOP-TARGET] {top_target['currency']}: {top_target['profit_potential']:.3f} profit, {top_target['strategic_score']:.3f} score")

            return selected_currencies

        except Exception as e:
            logger.error(f"❌ [STRATEGIC-SELECTION] Error in strategic selection: {e}")
            # Fallback to basic profitable pairs
            return ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'AVAX']

    async def _apply_strategic_diversification_rules(self, strategic_targets: List[str], current_holdings: set, exchange_name: str) -> List[str]:
        """Apply diversification rules while maintaining profit focus"""
        try:
            diversified_targets = []

            # Rule 1: Prioritize high-profit targets not currently held
            unowned_targets = [curr for curr in strategic_targets if curr not in current_holdings]
            diversified_targets.extend(unowned_targets[:8])  # Top 8 unowned profitable targets

            # Rule 2: Add some current holdings for rebalancing (if profitable)
            owned_profitable = [curr for curr in strategic_targets if curr in current_holdings]
            diversified_targets.extend(owned_profitable[:4])  # Top 4 owned profitable targets

            # Rule 3: Ensure major cryptocurrencies are included (for liquidity and stability)
            major_cryptos = ['BTC', 'ETH', 'SOL']
            for crypto in major_cryptos:
                if crypto not in diversified_targets and len(diversified_targets) < 15:
                    diversified_targets.append(crypto)

            # Rule 4: Apply anti-correlation diversification
            final_targets = await self._apply_correlation_diversification(diversified_targets, exchange_name)

            logger.info(f"🎯 [STRATEGIC-DIVERSIFICATION] Applied rules: {len(strategic_targets)} -> {len(final_targets)} targets")

            return final_targets

        except Exception as e:
            logger.error(f"❌ [STRATEGIC-DIVERSIFICATION] Error applying rules: {e}")
            return strategic_targets[:15]  # Fallback to top 15

    async def _calculate_market_based_strategic_score(self, currency: str, market_data: Dict, current_balances: Dict[str, float]) -> float:
        """Calculate strategic score using market-based analysis (no neural dependencies)"""
        try:
            score = 0.0

            # Market momentum score (40% weight)
            momentum_24h = market_data.get('momentum_24h', 0.02)
            volume_change = market_data.get('volume_change', 0.15)
            momentum_score = min((abs(momentum_24h) + volume_change) / 0.3, 1.0)
            score += momentum_score * 0.4

            # Volatility score (25% weight) - moderate volatility is preferred
            volatility = market_data.get('volatility', 0.3)
            volatility_score = 1.0 - abs(volatility - 0.25) / 0.25  # Optimal around 25%
            volatility_score = max(0.0, min(1.0, volatility_score))
            score += volatility_score * 0.25

            # Diversification value (20% weight)
            is_currently_held = currency in current_balances
            diversification_score = 0.8 if not is_currently_held else 0.3  # Prefer new currencies
            score += diversification_score * 0.2

            # Liquidity score (15% weight)
            liquidity_score = self._calculate_liquidity_score(currency, market_data)
            score += liquidity_score * 0.15

            return min(score, 1.0)

        except Exception as e:
            logger.error(f"❌ [MARKET-STRATEGIC-SCORE] Error calculating score for {currency}: {e}")
            return 0.5  # Neutral score

    async def _calculate_strategic_score(self, currency: str, profit_prediction: Dict, market_data: Dict, current_balances: Dict[str, float]) -> float:
        """Calculate strategic score combining profit potential, market conditions, and diversification value"""
        try:
            score = 0.0

            # Base profit score (40% weight)
            profit_potential = profit_prediction.get('predicted_profit', 0)
            profit_score = min(profit_potential * 10, 1.0)  # Cap at 1.0
            score += profit_score * 0.4

            # Market momentum score (25% weight)
            momentum_24h = market_data.get('change_24h', 0)
            volume_change = market_data.get('volume_change', 0)
            momentum_score = min((abs(momentum_24h) + volume_change) / 0.2, 1.0)
            score += momentum_score * 0.25

            # Diversification value (20% weight)
            is_currently_held = currency in current_balances
            diversification_score = 0.8 if not is_currently_held else 0.3  # Prefer new currencies
            score += diversification_score * 0.2

            # Liquidity score (15% weight)
            liquidity_score = self._calculate_liquidity_score(currency, market_data)
            score += liquidity_score * 0.15

            return min(score, 1.0)

        except Exception as e:
            logger.error(f"❌ [STRATEGIC-SCORE] Error calculating score for {currency}: {e}")
            return 0.5  # Neutral score

    async def _get_market_data_for_analysis(self, symbol: str, exchange_name: str) -> Dict[str, float]:
        """Get market data for strategic analysis using FAST price fetching"""
        try:
            client = self.exchange_clients.get(exchange_name)
            if not client:
                return {}

            # FAST PRICE FETCH: Use optimized fast price method
            if hasattr(client, 'get_price_fast'):
                current_price = client.get_price_fast(symbol)
            else:
                current_price = client.get_price(symbol)

            if not current_price:
                return {}

            # Simulate market data (in production, this would come from real market data feeds)
            market_data = {
                'current_price': float(current_price),
                'change_24h': 0.02,  # 2% change (would be real data)
                'volume_change': 0.15,  # 15% volume change (would be real data)
                'volatility': 0.3,  # 30% volatility (would be calculated from real data)
                'rsi': 45.0,  # RSI indicator (would be calculated from real data)
                'momentum_1h': 0.01,  # 1% 1-hour momentum
                'momentum_24h': 0.02,  # 2% 24-hour momentum
                'volume': 1000000.0,  # Trading volume
                'market_cap_rank': 50  # Market cap ranking
            }

            return market_data

        except Exception as e:
            logger.error(f"❌ [MARKET-DATA] Error getting data for {symbol}: {e}")
            return {}

    def _calculate_liquidity_score(self, currency: str, market_data: Dict) -> float:
        """Calculate liquidity score for a currency"""
        try:
            # Major currencies get high liquidity scores
            major_currencies = {'BTC': 1.0, 'ETH': 0.95, 'SOL': 0.9, 'ADA': 0.85, 'DOT': 0.8}
            if currency in major_currencies:
                return major_currencies[currency]

            # For other currencies, use volume and market cap as proxies
            volume = market_data.get('volume', 0)
            market_cap_rank = market_data.get('market_cap_rank', 1000)

            # Higher volume = higher liquidity
            volume_score = min(volume / 10000000, 1.0)  # Normalize to 10M volume

            # Lower market cap rank = higher liquidity
            rank_score = max(0, 1 - (market_cap_rank / 500))  # Top 500 coins

            return (volume_score * 0.6 + rank_score * 0.4)

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY-SCORE] Error calculating for {currency}: {e}")
            return 0.5

    async def _apply_correlation_diversification(self, targets: List[str], exchange_name: str) -> List[str]:
        """Apply correlation-based diversification to reduce portfolio risk"""
        try:
            # Simple correlation diversification (in production, would use real correlation data)
            diversified = []

            # Group currencies by type for diversification
            currency_groups = {
                'major': ['BTC', 'ETH'],
                'smart_contract': ['SOL', 'ADA', 'DOT', 'AVAX', 'MATIC'],
                'defi': ['UNI', 'AAVE', 'COMP', 'MKR', 'SNX', 'CRV'],
                'layer2': ['ARB', 'OP', 'MATIC'],
                'meme': ['DOGE', 'SHIB', 'PEPE'],
                'utility': ['LINK', 'VET', 'FIL', 'THETA']
            }

            # Select from each group to ensure diversification
            for group_name, group_currencies in currency_groups.items():
                group_targets = [curr for curr in targets if curr in group_currencies]
                if group_targets:
                    # Add top 2 from each group
                    diversified.extend(group_targets[:2])
                    if len(diversified) >= 15:
                        break

            # Fill remaining slots with top targets
            for target in targets:
                if target not in diversified and len(diversified) < 20:
                    diversified.append(target)

            logger.info(f"🎯 [CORRELATION-DIVERSIFICATION] Applied correlation rules: {len(targets)} -> {len(diversified)}")

            return diversified

        except Exception as e:
            logger.error(f"❌ [CORRELATION-DIVERSIFICATION] Error: {e}")
            return targets[:15]

    async def _create_diversified_buy_opportunity(self, exchange_name: str, target_currency: str, usdt_balance: float) -> Optional[TradingOpportunity]:
        """Create a strategic diversified BUY opportunity with profit analysis"""
        try:
            symbol = f"{target_currency}USDT"

            # STRATEGIC ENHANCEMENT: Get market data and profit prediction
            market_data = await self._get_market_data_for_analysis(symbol, exchange_name)
            if not market_data:
                logger.debug(f"⚠️ [STRATEGIC-BUY] No market data for {symbol}")
                return None

            # Check if this symbol exists in our active pairs
            pair = None
            if exchange_name in self.active_pairs and symbol in self.active_pairs[exchange_name]:
                pair = self.active_pairs[exchange_name][symbol]
            else:
                # Create a temporary pair if not found
                pair = CurrencyPair(
                    base=target_currency,
                    quote='USDT',
                    symbol=symbol,
                    exchange=exchange_name,
                    min_order_value=Decimal('5.0'),
                    tick_size=Decimal('0.00000001'),
                    lot_size=Decimal('0.00000001'),
                    is_active=True,
                    liquidity_score=1.0
                )

            # FAST PRICE FETCH: Use optimized fast price method
            client = self.exchange_clients[exchange_name]
            if hasattr(client, 'get_price_fast'):
                price = client.get_price_fast(symbol)
            else:
                price = client.get_price(symbol)

            if price and float(price) > 0:
                # STRATEGIC ENHANCEMENT: Calculate buy amount based on profit potential
                profit_potential = market_data.get('momentum_24h', 0.02)  # Use momentum as profit proxy
                strategic_score = await self._calculate_strategic_score(target_currency, {'predicted_profit': profit_potential}, market_data, {})

                # Dynamic buy percentage based on strategic score
                base_percentage = 0.15  # 15% base
                strategic_bonus = strategic_score * 0.15  # Up to 15% bonus
                buy_percentage = min(0.30, base_percentage + strategic_bonus)  # Cap at 30%

                buy_amount = usdt_balance * buy_percentage

                # Ensure minimum order value
                if buy_amount < 5.0:
                    buy_amount = min(5.0, usdt_balance * 0.9)  # Use up to 90% if needed for minimum

                if buy_amount >= 5.0:  # Only create opportunity if we meet minimum
                    # STRATEGIC ENHANCEMENT: Calculate confidence based on multiple factors
                    diversification_bonus = self._calculate_currency_diversification_bonus(target_currency)
                    liquidity_score = self._calculate_liquidity_score(target_currency, market_data)

                    # Weighted confidence calculation
                    base_confidence = 0.65
                    strategic_confidence = strategic_score * 0.20
                    diversification_confidence = diversification_bonus * 0.10
                    liquidity_confidence = liquidity_score * 0.05

                    confidence = min(0.95, base_confidence + strategic_confidence + diversification_confidence + liquidity_confidence)

                    # STRATEGIC ENHANCEMENT: Calculate expected profit based on analysis
                    expected_profit_rate = max(0.015, profit_potential)  # Minimum 1.5% expected
                    expected_profit = buy_amount * expected_profit_rate

                    # Risk score based on volatility and strategic score
                    volatility = market_data.get('volatility', 0.3)
                    risk_score = max(0.2, min(0.8, volatility * (1 - strategic_score)))

                    # Execution priority based on profit potential
                    if profit_potential > 0.05:  # >5% profit potential
                        execution_priority = 1  # High priority
                    elif profit_potential > 0.03:  # >3% profit potential
                        execution_priority = 2  # Medium-high priority
                    else:
                        execution_priority = 3  # Medium priority

                    opportunity = TradingOpportunity(
                        pair=pair,
                        side='buy',
                        amount=Decimal(str(buy_amount)),
                        price=Decimal(str(price)),
                        expected_profit=Decimal(str(expected_profit)),
                        confidence=confidence,
                        strategy='strategic_diversified_buying',
                        risk_score=risk_score,
                        execution_priority=execution_priority
                    )

                    logger.info(f"🎯 [STRATEGIC-BUY] Created {target_currency} opportunity:")
                    logger.info(f"    💰 Amount: ${buy_amount:.2f} ({buy_percentage:.1%} of USDT)")
                    logger.info(f"    📊 Confidence: {confidence:.3f} | Strategic Score: {strategic_score:.3f}")
                    logger.info(f"    💹 Expected Profit: ${expected_profit:.2f} ({expected_profit_rate:.2%})")
                    logger.info(f"    ⚡ Priority: {execution_priority} | Risk: {risk_score:.3f}")

                    return opportunity
                else:
                    logger.debug(f"🎯 [STRATEGIC-BUY] Insufficient USDT for {target_currency}: ${buy_amount:.2f} < $5.00")
                    return None
            else:
                logger.warning(f"⚠️ [STRATEGIC-BUY] Could not get price for {symbol}")
                return None

        except Exception as e:
            logger.error(f"❌ [STRATEGIC-BUY] Error creating opportunity for {target_currency}: {e}")
            return None

    def _calculate_currency_diversification_bonus(self, currency: str) -> float:
        """Calculate diversification bonus for a currency"""
        try:
            # Check how often we've traded this currency recently
            recent_trades = self.last_traded_pairs[:10]
            currency_pairs = [pair for pair in recent_trades if pair.startswith(currency)]

            # Bonus for less-traded currencies
            if len(currency_pairs) == 0:
                return 0.15  # 15% bonus for never traded
            elif len(currency_pairs) <= 2:
                return 0.10  # 10% bonus for lightly traded
            elif len(currency_pairs) <= 4:
                return 0.05  # 5% bonus for moderately traded
            else:
                return 0.0   # No bonus for heavily traded

        except Exception as e:
            logger.error(f"❌ [DIVERSIFICATION-BONUS] Error calculating bonus for {currency}: {e}")
            return 0.0

    async def _find_arbitrage_opportunities(self) -> List[TradingOpportunity]:
        """Find cross-exchange arbitrage opportunities"""
        opportunities = []
        
        try:
            # Compare prices across exchanges for same pairs
            common_symbols = set()
            
            # Find symbols that exist on multiple exchanges
            for exchange_pairs in self.active_pairs.values():
                common_symbols.update(exchange_pairs.keys())
            
            for symbol in common_symbols:
                exchange_prices = {}
                
                # Get prices from all exchanges that have this symbol
                for exchange_name, exchange_pairs in self.active_pairs.items():
                    if symbol in exchange_pairs:
                        try:
                            client = self.exchange_clients[exchange_name]
                            price = float(client.get_price(symbol))
                            if price > 0:
                                exchange_prices[exchange_name] = price
                        except Exception:
                            continue
                
                # Look for arbitrage opportunities
                if len(exchange_prices) >= 2:
                    min_price_exchange = min(exchange_prices, key=exchange_prices.get)
                    max_price_exchange = max(exchange_prices, key=exchange_prices.get)
                    
                    min_price = exchange_prices[min_price_exchange]
                    max_price = exchange_prices[max_price_exchange]
                    
                    # Calculate potential profit (accounting for fees)
                    price_diff_pct = (max_price - min_price) / min_price
                    
                    if price_diff_pct > 0.005:  # 0.5% minimum profit threshold
                        # Create arbitrage opportunity
                        pair = self.active_pairs[min_price_exchange][symbol]
                        
                        opportunity = TradingOpportunity(
                            pair=pair,
                            side='buy',
                            amount=Decimal('100'),  # To be calculated based on balance
                            price=Decimal(str(min_price)),
                            expected_profit=Decimal(str(price_diff_pct * 100)),
                            confidence=0.9,
                            strategy='cross_exchange_arbitrage',
                            risk_score=0.2,
                            execution_priority=1
                        )
                        opportunities.append(opportunity)
            
            logger.info(f"🎯 [ARBITRAGE] Found {len(opportunities)} arbitrage opportunities")
            
        except Exception as e:
            logger.error(f"❌ [ARBITRAGE] Error finding arbitrage opportunities: {e}")
        
        return opportunities
    
    async def _find_triangular_arbitrage(self) -> List[TradingOpportunity]:
        """Find triangular arbitrage with PERFORMANCE OPTIMIZATIONS to prevent hanging"""
        opportunities = []

        # PERFORMANCE LIMITS to prevent infinite loops
        MAX_CURRENCIES = 20  # Limit to top 20 currencies by volume
        MAX_COMBINATIONS = 1000  # Maximum combinations to test
        TIMEOUT_SECONDS = 30  # Maximum time to spend on triangular arbitrage

        import time
        import asyncio
        start_time = time.time()

        try:
            for exchange_name, exchange_pairs in self.active_pairs.items():
                logger.info(f"🔍 [TRIANGULAR] Scanning {len(exchange_pairs)} pairs for arbitrage on {exchange_name}")

                # Get all available currencies from the exchange pairs
                available_currencies = set()
                currency_volumes = {}  # Track currency trading volumes

                for pair_symbol, pair_info in exchange_pairs.items():
                    base = pair_info.base
                    quote = pair_info.quote
                    available_currencies.add(base)
                    available_currencies.add(quote)

                    # Track currency frequency as proxy for volume
                    currency_volumes[base] = currency_volumes.get(base, 0) + 1
                    currency_volumes[quote] = currency_volumes.get(quote, 0) + 1

                # OPTIMIZATION 1: Filter to high-volume currencies only
                priority_currencies = ['USDT', 'BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'MATIC', 'AVAX', 'LINK', 'UNI']

                # Sort currencies by volume/frequency and priority
                sorted_currencies = sorted(
                    available_currencies,
                    key=lambda c: (
                        c in priority_currencies,  # Priority currencies first
                        currency_volumes.get(c, 0)  # Then by volume
                    ),
                    reverse=True
                )

                # Limit to top currencies to prevent combinatorial explosion
                limited_currencies = sorted_currencies[:MAX_CURRENCIES]

                logger.info(f"🔍 [TRIANGULAR] Optimized: {len(available_currencies)} -> {len(limited_currencies)} currencies")
                logger.info(f"🔍 [TRIANGULAR] Top currencies: {limited_currencies[:10]}")

                # OPTIMIZATION 2: Use itertools.permutations with early termination
                import itertools
                combinations_tested = 0

                for curr_a, curr_b, curr_c in itertools.permutations(limited_currencies, 3):
                    # TIMEOUT CHECK: Prevent infinite loops
                    if time.time() - start_time > TIMEOUT_SECONDS:
                        logger.warning(f"⏰ [TRIANGULAR] Timeout reached ({TIMEOUT_SECONDS}s) - stopping arbitrage scan")
                        break

                    # COMBINATION LIMIT: Prevent excessive computation
                    combinations_tested += 1
                    if combinations_tested > MAX_COMBINATIONS:
                        logger.warning(f"🔢 [TRIANGULAR] Combination limit reached ({MAX_COMBINATIONS}) - stopping scan")
                        break

                    # Yield control periodically to prevent blocking
                    if combinations_tested % 100 == 0:
                        await asyncio.sleep(0.001)  # 1ms yield every 100 combinations

                    # Find trading pairs for this triangle
                    pair_ab = self._find_trading_pair(exchange_pairs, curr_a, curr_b)
                    pair_bc = self._find_trading_pair(exchange_pairs, curr_b, curr_c)
                    pair_ca = self._find_trading_pair(exchange_pairs, curr_c, curr_a)

                    # All three pairs must exist for triangular arbitrage
                    if pair_ab and pair_bc and pair_ca:
                        try:
                            client = self.exchange_clients[exchange_name]

                            # Get prices for all three pairs
                            price_ab = float(client.get_price(pair_ab))
                            price_bc = float(client.get_price(pair_bc))
                            price_ca = float(client.get_price(pair_ca))

                            if all(p > 0 for p in [price_ab, price_bc, price_ca]):
                                # Calculate triangular arbitrage profit
                                # Start with 1 unit of curr_a
                                step1 = 1.0 / price_ab  # curr_a -> curr_b
                                step2 = step1 / price_bc  # curr_b -> curr_c
                                step3 = step2 / price_ca  # curr_c -> curr_a

                                profit = step3 - 1.0

                                if profit > 0.001:  # 0.1% minimum profit
                                    opportunity = TradingOpportunity(
                                        pair=exchange_pairs[pair_ab],
                                        side='sell',
                                        amount=Decimal('10'),  # To be calculated
                                        price=Decimal(str(price_ab)),
                                        expected_profit=Decimal(str(profit * 100)),
                                        confidence=0.85,
                                        strategy='triangular_arbitrage',
                                        risk_score=0.25,
                                        execution_priority=1
                                    )
                                    opportunities.append(opportunity)

                                    # OPTIMIZATION 3: Early exit if we find good opportunities
                                    if len(opportunities) >= 5:  # Limit to top 5 opportunities
                                        logger.info(f"🎯 [TRIANGULAR] Found {len(opportunities)} opportunities - early exit")
                                        break

                        except Exception:
                            continue

                # Break outer loop if timeout reached
                if time.time() - start_time > TIMEOUT_SECONDS:
                    break

            elapsed_time = time.time() - start_time
            logger.info(f"🎯 [TRIANGULAR] Found {len(opportunities)} triangular arbitrage opportunities in {elapsed_time:.2f}s")

        except Exception as e:
            logger.error(f"❌ [TRIANGULAR] Error finding triangular arbitrage: {e}")

        return opportunities
    
    async def _find_rebalancing_opportunities(self) -> List[TradingOpportunity]:
        """Find portfolio rebalancing opportunities"""
        opportunities = []
        
        try:
            # Calculate current portfolio allocation
            total_portfolio_value = Decimal('0')
            currency_values = {}
            
            for exchange_name, balances in self.currency_balances.items():
                for currency, balance in balances.items():
                    if balance > 0:
                        # Get USD value of this currency
                        usd_value = await self._get_usd_value(currency, balance, exchange_name)
                        currency_values[currency] = currency_values.get(currency, Decimal('0')) + usd_value
                        total_portfolio_value += usd_value
            
            if total_portfolio_value > 0:
                # Calculate target allocations (equal weight for simplicity)
                target_allocation = Decimal('1') / Decimal(str(len(currency_values)))
                
                for currency, current_value in currency_values.items():
                    current_allocation = current_value / total_portfolio_value
                    allocation_diff = abs(current_allocation - target_allocation)
                    
                    # If allocation is significantly off target, create rebalancing opportunity
                    if allocation_diff > Decimal('0.1'):  # 10% threshold
                        # Determine if we need to buy or sell this currency
                        if current_allocation > target_allocation:
                            # Overweight - need to sell
                            side = 'sell'
                        else:
                            # Underweight - need to buy
                            side = 'buy'
                        
                        # Find a suitable trading pair for this currency
                        for exchange_name, exchange_pairs in self.active_pairs.items():
                            for symbol, pair in exchange_pairs.items():
                                if (side == 'sell' and pair.base == currency) or (side == 'buy' and pair.quote == currency):
                                    opportunity = TradingOpportunity(
                                        pair=pair,
                                        side=side,
                                        amount=current_value * allocation_diff,
                                        price=Decimal('0'),  # To be determined
                                        expected_profit=Decimal('0'),
                                        confidence=0.7,
                                        strategy='portfolio_rebalancing',
                                        risk_score=0.4,
                                        execution_priority=3
                                    )
                                    opportunities.append(opportunity)
                                    break
            
            logger.info(f"🎯 [REBALANCING] Found {len(opportunities)} rebalancing opportunities")
            
        except Exception as e:
            logger.error(f"❌ [REBALANCING] Error finding rebalancing opportunities: {e}")
        
        return opportunities
    
    async def _get_usd_value(self, currency: str, amount: float, exchange_name: str) -> Decimal:
        """Get USD value of a currency amount"""
        try:
            if currency in ['USD', 'USDT', 'USDC']:
                return Decimal(str(amount))
            
            # Try to get price from exchange
            client = self.exchange_clients[exchange_name]
            symbol = f"{currency}USDT"
            
            if hasattr(client, 'get_price'):
                price = client.get_price(symbol)
                if price and float(price) > 0:
                    return Decimal(str(amount)) * Decimal(str(price))
            
            return Decimal('0')
            
        except Exception as e:
            logger.debug(f"Error getting USD value for {currency}: {e}")
            return Decimal('0')
    
    async def initialize_market_data_feeds(self):
        """Initialize real-time market data feeds"""
        logger.info("📡 [MARKET-DATA] Initializing market data feeds...")
        # Implementation for real-time market data
        pass
    
    async def initialize_risk_management(self):
        """Initialize risk management systems"""
        logger.info("🛡️ [RISK-MGMT] Initializing risk management...")
        # Implementation for risk management
        pass

    async def initialize_arbitrage_engine(self):
        """Initialize the cross-currency arbitrage engine"""
        try:
            if CrossCurrencyArbitrageEngine is None:
                logger.warning("⚠️ [ARBITRAGE] CrossCurrencyArbitrageEngine not available")
                return

            logger.info("🔄 [ARBITRAGE] Initializing cross-currency arbitrage engine...")

            self.arbitrage_engine = CrossCurrencyArbitrageEngine(
                exchange_clients=self.exchange_clients,
                config={
                    'min_profit_threshold': 0.002,  # 0.2% minimum profit
                    'max_execution_time': 5.0,      # 5 seconds max execution
                    'min_capital': 10.0             # $10 minimum capital
                }
            )

            await self.arbitrage_engine.initialize()

            logger.info("✅ [ARBITRAGE] Cross-currency arbitrage engine initialized")

        except Exception as e:
            logger.error(f"❌ [ARBITRAGE] Error initializing arbitrage engine: {e}")

    async def _find_arbitrage_opportunities(self) -> List[TradingOpportunity]:
        """Find arbitrage opportunities and convert to trading opportunities"""
        arbitrage_trading_opportunities = []

        try:
            if not self.arbitrage_engine:
                return arbitrage_trading_opportunities

            # Find all arbitrage opportunities
            arbitrage_opportunities = await self.arbitrage_engine.find_all_arbitrage_opportunities()

            # Convert arbitrage opportunities to trading opportunities
            for arb_opp in arbitrage_opportunities:
                try:
                    # Create a trading opportunity from arbitrage opportunity
                    if arb_opp.symbol_path:
                        symbol = arb_opp.symbol_path[0]

                        # Determine the primary exchange and currency pair
                        exchange = arb_opp.exchanges[0] if arb_opp.exchanges else 'bybit'

                        # Create currency pair object
                        if exchange in self.active_pairs and symbol in self.active_pairs[exchange]:
                            pair = self.active_pairs[exchange][symbol]
                        else:
                            # Create a temporary pair object
                            from .multi_currency_trading_engine import CurrencyPair
                            pair = CurrencyPair(
                                base=arb_opp.currency_path[0] if arb_opp.currency_path else 'BTC',
                                quote=arb_opp.currency_path[1] if len(arb_opp.currency_path) > 1 else 'USDT',
                                symbol=symbol,
                                exchange=exchange,
                                min_order_value=arb_opp.min_capital_required,
                                tick_size=Decimal('0.00000001'),
                                lot_size=Decimal('0.00000001'),
                                is_active=True,
                                liquidity_score=1.0
                            )

                        # Determine side based on arbitrage type
                        side = 'buy'  # Default to buy for arbitrage
                        if arb_opp.arbitrage_type.value == 'triangular':
                            side = 'sell'  # Start with sell for triangular

                        trading_opportunity = TradingOpportunity(
                            pair=pair,
                            side=side,
                            amount=arb_opp.amounts[0] if arb_opp.amounts else Decimal('10'),
                            price=arb_opp.price_path[0] if arb_opp.price_path else Decimal('0'),
                            expected_profit=arb_opp.expected_profit,
                            confidence=arb_opp.confidence,
                            strategy=f"arbitrage_{arb_opp.arbitrage_type.value}",
                            risk_score=arb_opp.risk_score,
                            execution_priority=1  # High priority for arbitrage
                        )

                        arbitrage_trading_opportunities.append(trading_opportunity)

                except Exception as e:
                    logger.warning(f"⚠️ [ARBITRAGE] Error converting opportunity: {e}")
                    continue

            logger.info(f"🔄 [ARBITRAGE] Converted {len(arbitrage_trading_opportunities)} arbitrage opportunities")

        except Exception as e:
            logger.error(f"❌ [ARBITRAGE] Error finding arbitrage opportunities: {e}")

        return arbitrage_trading_opportunities

    async def initialize_portfolio_rebalancer(self):
        """Initialize the dynamic portfolio rebalancer"""
        try:
            if DynamicPortfolioRebalancer is None:
                logger.warning("⚠️ [REBALANCER] DynamicPortfolioRebalancer not available")
                return

            logger.info("📊 [REBALANCER] Initializing dynamic portfolio rebalancer...")

            self.portfolio_rebalancer = DynamicPortfolioRebalancer(
                exchange_clients=self.exchange_clients,
                config={
                    'strategy': 'volatility_adjusted',  # Use volatility-adjusted strategy
                    'threshold': 0.05,                  # 5% rebalancing threshold
                    'min_amount': 10.0,                 # $10 minimum rebalancing amount
                    'max_single': 0.2,                  # 20% max single rebalance
                    'max_allocation': 0.4,              # 40% max per currency
                    'min_allocation': 0.05,             # 5% min per currency
                    'volatility_days': 30               # 30-day volatility lookback
                }
            )

            await self.portfolio_rebalancer.initialize()

            logger.info("✅ [REBALANCER] Dynamic portfolio rebalancer initialized")

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error initializing portfolio rebalancer: {e}")

    async def _find_portfolio_rebalancing_opportunities(self) -> List[TradingOpportunity]:
        """Find portfolio rebalancing opportunities and convert to trading opportunities"""
        rebalancing_trading_opportunities = []

        try:
            if not self.portfolio_rebalancer:
                return rebalancing_trading_opportunities

            # Find rebalancing actions
            rebalancing_actions = await self.portfolio_rebalancer.identify_rebalancing_needs()

            # Convert rebalancing actions to trading opportunities
            for rebalance_action in rebalancing_actions:
                try:
                    # Determine the trading symbol and side
                    if rebalance_action.currency_from == 'USDT':
                        # Buying crypto with USDT
                        symbol = f"{rebalance_action.currency_to}USDT"
                        side = 'buy'
                        base_currency = rebalance_action.currency_to
                        quote_currency = 'USDT'
                    elif rebalance_action.currency_to == 'USDT':
                        # Selling crypto for USDT
                        symbol = f"{rebalance_action.currency_from}USDT"
                        side = 'sell'
                        base_currency = rebalance_action.currency_from
                        quote_currency = 'USDT'
                    else:
                        # Direct crypto-to-crypto trade
                        symbol = f"{rebalance_action.currency_from}{rebalance_action.currency_to}"
                        side = 'sell'
                        base_currency = rebalance_action.currency_from
                        quote_currency = rebalance_action.currency_to

                    # Find the exchange and create currency pair
                    exchange = list(self.exchange_clients.keys())[0]  # Use first available exchange

                    # Create currency pair object
                    pair = CurrencyPair(
                        base=base_currency,
                        quote=quote_currency,
                        symbol=symbol,
                        exchange=exchange,
                        min_order_value=Decimal('5.0'),
                        tick_size=Decimal('0.00000001'),
                        lot_size=Decimal('0.00000001'),
                        is_active=True,
                        liquidity_score=1.0
                    )

                    # Get current price for amount calculation
                    try:
                        client = self.exchange_clients[exchange]
                        price = client.get_price(symbol)
                        current_price = Decimal(str(price)) if price and float(price) > 0 else Decimal('0')
                    except Exception:
                        current_price = Decimal('0')

                    # Calculate trade amount
                    if side == 'buy':
                        # Amount in quote currency (USDT)
                        trade_amount = rebalance_action.amount
                    else:
                        # Amount in base currency
                        if current_price > 0:
                            trade_amount = rebalance_action.amount / current_price
                        else:
                            trade_amount = Decimal('0')

                    if trade_amount > 0:
                        trading_opportunity = TradingOpportunity(
                            pair=pair,
                            side=side,
                            amount=trade_amount,
                            price=current_price,
                            expected_profit=Decimal('0'),  # Rebalancing for portfolio optimization
                            confidence=0.8,  # High confidence for rebalancing
                            strategy="portfolio_rebalancing",
                            risk_score=rebalance_action.risk_adjustment,
                            execution_priority=rebalance_action.priority
                        )

                        rebalancing_trading_opportunities.append(trading_opportunity)

                except Exception as e:
                    logger.warning(f"⚠️ [REBALANCER] Error converting rebalancing action: {e}")
                    continue

            logger.info(f"📊 [REBALANCER] Converted {len(rebalancing_trading_opportunities)} rebalancing opportunities")

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error finding rebalancing opportunities: {e}")

        return rebalancing_trading_opportunities

    async def initialize_order_executor(self):
        """Initialize the advanced order executor"""
        try:
            if AdvancedOrderExecutor is None:
                logger.warning("⚠️ [ORDER-EXECUTOR] AdvancedOrderExecutor not available")
                return

            logger.info("⚡ [ORDER-EXECUTOR] Initializing advanced order executor...")

            self.order_executor = AdvancedOrderExecutor(
                exchange_clients=self.exchange_clients,
                config={
                    'participation_rate': 0.1,     # 10% default participation
                    'slice_duration': 30,          # 30 second default slices
                    'max_duration': 3600,          # 1 hour max execution
                    'iceberg_threshold': 0.8       # 80% refresh threshold
                }
            )

            await self.order_executor.initialize()

            logger.info("✅ [ORDER-EXECUTOR] Advanced order executor initialized")

        except Exception as e:
            logger.error(f"❌ [ORDER-EXECUTOR] Error initializing order executor: {e}")

    async def execute_advanced_order(self, opportunity: TradingOpportunity,
                                   order_type: str = "market") -> Dict[str, Any]:
        """Execute a trading opportunity using advanced order types"""
        try:
            logger.info(f"⚡ [ADVANCED-EXEC] Executing {order_type} order for {opportunity.strategy}")

            # Determine if we should use advanced order types
            use_advanced = await self._should_use_advanced_order_type(opportunity)

            if use_advanced and self.order_executor and AdvancedOrder:
                # Create advanced order
                advanced_order = AdvancedOrder(
                    order_id=f"adv_{int(time.time())}_{opportunity.pair.symbol}",
                    symbol=opportunity.pair.symbol,
                    side=opportunity.side,
                    total_amount=opportunity.amount,
                    order_type=OrderType(order_type) if order_type in [e.value for e in OrderType] else OrderType.MARKET,
                    exchange=opportunity.pair.exchange,
                    execution_duration=300,  # 5 minutes default
                    num_slices=5,           # 5 slices default
                    participation_rate=0.15, # 15% participation
                    visible_amount=opportunity.amount * Decimal('0.2') if order_type == "iceberg" else None
                )

                # Execute using advanced order executor
                result = await self.order_executor.submit_advanced_order(advanced_order)

                if result.get('success', False):
                    logger.info(f"✅ [ADVANCED-EXEC] Advanced order executed successfully")
                    return result
                else:
                    logger.warning(f"⚠️ [ADVANCED-EXEC] Advanced order failed, falling back to standard execution")
                    # Fall back to standard execution
                    return await self.execute_trading_opportunity(opportunity)
            else:
                # Use standard execution
                return await self.execute_trading_opportunity(opportunity)

        except Exception as e:
            logger.error(f"❌ [ADVANCED-EXEC] Error executing advanced order: {e}")
            return {"success": False, "error": str(e)}

    async def _should_use_advanced_order_type(self, opportunity: TradingOpportunity) -> bool:
        """Determine if advanced order types should be used"""
        try:
            # Use advanced order types for larger orders or specific strategies
            large_order_threshold = Decimal('100')  # $100 threshold

            # Calculate order value
            order_value = opportunity.amount * opportunity.price if opportunity.price > 0 else opportunity.amount

            # Use advanced orders for:
            # 1. Large orders
            # 2. Arbitrage strategies (need precise timing)
            # 3. Portfolio rebalancing (need to minimize market impact)
            if (order_value >= large_order_threshold or
                'arbitrage' in opportunity.strategy or
                'rebalancing' in opportunity.strategy):
                return True

            return False

        except Exception as e:
            logger.error(f"❌ [ADVANCED-EXEC] Error determining order type: {e}")
            return False

    async def execute_twap_order(self, opportunity: TradingOpportunity,
                               duration_minutes: int = 5) -> Dict[str, Any]:
        """Execute a TWAP (Time-Weighted Average Price) order"""
        try:
            if not self.order_executor or not AdvancedOrder:
                return await self.execute_trading_opportunity(opportunity)

            logger.info(f"⏰ [TWAP] Executing TWAP order over {duration_minutes} minutes")

            twap_order = AdvancedOrder(
                order_id=f"twap_{int(time.time())}_{opportunity.pair.symbol}",
                symbol=opportunity.pair.symbol,
                side=opportunity.side,
                total_amount=opportunity.amount,
                order_type=OrderType.TWAP,
                exchange=opportunity.pair.exchange,
                execution_duration=duration_minutes * 60,  # Convert to seconds
                num_slices=max(1, duration_minutes)        # 1 slice per minute
            )

            return await self.order_executor.submit_advanced_order(twap_order)

        except Exception as e:
            logger.error(f"❌ [TWAP] Error executing TWAP order: {e}")
            return {"success": False, "error": str(e)}

    async def execute_vwap_order(self, opportunity: TradingOpportunity,
                               participation_rate: float = 0.1) -> Dict[str, Any]:
        """Execute a VWAP (Volume-Weighted Average Price) order"""
        try:
            if not self.order_executor or not AdvancedOrder:
                return await self.execute_trading_opportunity(opportunity)

            logger.info(f"📊 [VWAP] Executing VWAP order with {participation_rate*100:.1f}% participation")

            vwap_order = AdvancedOrder(
                order_id=f"vwap_{int(time.time())}_{opportunity.pair.symbol}",
                symbol=opportunity.pair.symbol,
                side=opportunity.side,
                total_amount=opportunity.amount,
                order_type=OrderType.VWAP,
                exchange=opportunity.pair.exchange,
                participation_rate=participation_rate,
                execution_duration=3600  # 1 hour default
            )

            return await self.order_executor.submit_advanced_order(vwap_order)

        except Exception as e:
            logger.error(f"❌ [VWAP] Error executing VWAP order: {e}")
            return {"success": False, "error": str(e)}

    async def execute_iceberg_order(self, opportunity: TradingOpportunity,
                                  visible_percentage: float = 0.2) -> Dict[str, Any]:
        """Execute an Iceberg order (hidden large order)"""
        try:
            if not self.order_executor or not AdvancedOrder:
                return await self.execute_trading_opportunity(opportunity)

            logger.info(f"🧊 [ICEBERG] Executing iceberg order with {visible_percentage*100:.1f}% visible")

            iceberg_order = AdvancedOrder(
                order_id=f"iceberg_{int(time.time())}_{opportunity.pair.symbol}",
                symbol=opportunity.pair.symbol,
                side=opportunity.side,
                total_amount=opportunity.amount,
                order_type=OrderType.ICEBERG,
                exchange=opportunity.pair.exchange,
                visible_amount=opportunity.amount * Decimal(str(visible_percentage)),
                refresh_amount=opportunity.amount * Decimal(str(visible_percentage * 0.8))
            )

            return await self.order_executor.submit_advanced_order(iceberg_order)

        except Exception as e:
            logger.error(f"❌ [ICEBERG] Error executing iceberg order: {e}")
            return {"success": False, "error": str(e)}

    async def initialize_liquidity_manager(self):
        """Initialize the intelligent liquidity manager"""
        try:
            if IntelligentLiquidityManager is None:
                logger.warning("⚠️ [LIQUIDITY] IntelligentLiquidityManager not available")
                return

            logger.info("💧 [LIQUIDITY] Initializing intelligent liquidity manager...")

            self.liquidity_manager = IntelligentLiquidityManager(
                exchange_clients=self.exchange_clients,
                config={
                    'minimum_balances': {
                        'USDT': Decimal('50'),    # $50 minimum USDT
                        'BTC': Decimal('20'),     # $20 minimum BTC
                        'ETH': Decimal('20'),     # $20 minimum ETH
                        'SOL': Decimal('15'),     # $15 minimum SOL
                        'ADA': Decimal('10'),     # $10 minimum ADA
                        'DOT': Decimal('10'),     # $10 minimum DOT
                    },
                    'target_multiplier': 2.0,        # 2x minimum as target
                    'emergency_threshold': 0.1,      # 10% of minimum
                    'rebalance_threshold': 0.3       # 30% imbalance
                }
            )

            await self.liquidity_manager.initialize()

            logger.info("✅ [LIQUIDITY] Intelligent liquidity manager initialized")

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error initializing liquidity manager: {e}")

    async def check_liquidity_before_trade(self, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Check liquidity requirements before executing a trade"""
        try:
            if not self.liquidity_manager:
                return {"sufficient": True, "reason": "No liquidity manager available"}

            # Get current liquidity status
            await self.liquidity_manager.assess_current_liquidity()

            # Check if we have sufficient liquidity for this trade
            required_currency = opportunity.pair.quote if opportunity.side == 'buy' else opportunity.pair.base
            required_amount = opportunity.amount * opportunity.price if opportunity.side == 'buy' else opportunity.amount

            # Get current balance
            current_liquidity = self.liquidity_manager.current_liquidity
            exchange_balances = current_liquidity['by_exchange'].get(opportunity.pair.exchange, {})
            available_balance = Decimal(str(exchange_balances.get(required_currency, 0)))

            if available_balance >= required_amount:
                return {
                    "sufficient": True,
                    "available": float(available_balance),
                    "required": float(required_amount),
                    "currency": required_currency
                }
            else:
                # Check if liquidity can be provided
                deficit = required_amount - available_balance

                # Try to find liquidity source
                source_exchange = await self.liquidity_manager._find_liquidity_source(
                    required_currency, deficit, opportunity.pair.exchange
                )

                if source_exchange:
                    return {
                        "sufficient": False,
                        "available": float(available_balance),
                        "required": float(required_amount),
                        "deficit": float(deficit),
                        "currency": required_currency,
                        "can_provide": True,
                        "source": source_exchange
                    }
                else:
                    return {
                        "sufficient": False,
                        "available": float(available_balance),
                        "required": float(required_amount),
                        "deficit": float(deficit),
                        "currency": required_currency,
                        "can_provide": False,
                        "reason": "No liquidity source available"
                    }

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY-CHECK] Error checking liquidity: {e}")
            return {"sufficient": False, "error": str(e)}

    async def ensure_liquidity_for_trade(self, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Ensure sufficient liquidity is available for a trade"""
        try:
            liquidity_check = await self.check_liquidity_before_trade(opportunity)

            if liquidity_check.get("sufficient", False):
                return {"success": True, "message": "Sufficient liquidity available"}

            if not liquidity_check.get("can_provide", False):
                return {"success": False, "error": liquidity_check.get("reason", "Insufficient liquidity")}

            # Attempt to provide liquidity
            logger.info(f"💧 [LIQUIDITY-PROVISION] Providing liquidity for {opportunity.pair.symbol}")

            required_currency = opportunity.pair.quote if opportunity.side == 'buy' else opportunity.pair.base
            deficit = Decimal(str(liquidity_check["deficit"]))
            source_exchange = liquidity_check["source"]

            # Create liquidity action
            liquidity_action = LiquidityAction(
                action_id=f"trade_liq_{int(time.time())}",
                action_type=LiquidityActionType.PROVISION,
                currency=required_currency,
                source_exchange=source_exchange,
                target_exchange=opportunity.pair.exchange,
                amount=deficit,
                priority=90,  # High priority for trading
                reason=f"Liquidity needed for {opportunity.strategy} trade",
                estimated_cost=await self.liquidity_manager._estimate_transfer_cost(required_currency, deficit),
                estimated_time=await self.liquidity_manager._estimate_transfer_time(source_exchange, opportunity.pair.exchange),
                dependencies=[]
            )

            # Execute liquidity provision
            result = await self.liquidity_manager.execute_liquidity_action(liquidity_action)

            if result.get("success", False):
                logger.info("✅ [LIQUIDITY-PROVISION] Liquidity provided successfully")
                return {"success": True, "message": "Liquidity provided", "action": result}
            else:
                logger.error(f"❌ [LIQUIDITY-PROVISION] Failed to provide liquidity: {result.get('error')}")
                return {"success": False, "error": result.get("error")}

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY-PROVISION] Error ensuring liquidity: {e}")
            return {"success": False, "error": str(e)}

    async def execute_trading_opportunity(self, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Execute a trading opportunity with comprehensive error handling"""
        try:
            logger.info(f"⚡ [EXECUTE] Executing {opportunity.strategy} opportunity")
            logger.info(f"⚡ [EXECUTE] {opportunity.side} {opportunity.amount:.6f} {opportunity.pair.symbol}")

            # Get the appropriate exchange client
            exchange_name = opportunity.pair.exchange
            if exchange_name not in self.exchange_clients:
                return {"error": f"Exchange {exchange_name} not available", "success": False}

            client = self.exchange_clients[exchange_name]

            # Pre-execution validation
            validation_result = await self._validate_execution(opportunity)
            if not validation_result['valid']:
                return {"error": validation_result['reason'], "success": False}

            # STRATEGY EXECUTION with detailed logging
            logger.info(f"🎯 [STRATEGY-EXECUTION] Strategy: {opportunity.strategy}")
            logger.info(f"🎯 [STRATEGY-EXECUTION] Pair: {opportunity.pair.base}/{opportunity.pair.quote}")
            logger.info(f"🎯 [STRATEGY-EXECUTION] Expected Profit: {opportunity.expected_profit:.4f}")
            logger.info(f"🎯 [STRATEGY-EXECUTION] Confidence: {opportunity.confidence:.3f}")
            logger.info(f"🎯 [STRATEGY-EXECUTION] Priority: {opportunity.execution_priority}")

            # Execute the trade based on strategy with detailed logging
            if opportunity.strategy == 'direct_currency_trading':
                logger.info(f"🎯 [STRATEGY-EXECUTION] Executing DIRECT CURRENCY TRADING")
                result = await self._execute_direct_trade(client, opportunity)
            elif opportunity.strategy == 'cross_exchange_arbitrage':
                logger.info(f"🎯 [STRATEGY-EXECUTION] Executing CROSS-EXCHANGE ARBITRAGE")
                result = await self._execute_arbitrage_trade(opportunity)
            elif opportunity.strategy == 'triangular_arbitrage':
                logger.info(f"🎯 [STRATEGY-EXECUTION] Executing TRIANGULAR ARBITRAGE")
                result = await self._execute_triangular_arbitrage(client, opportunity)
            elif opportunity.strategy == 'portfolio_rebalancing':
                logger.info(f"🎯 [STRATEGY-EXECUTION] Executing PORTFOLIO REBALANCING")
                result = await self._execute_rebalancing_trade(client, opportunity)
            else:
                logger.info(f"🎯 [STRATEGY-EXECUTION] Executing STANDARD TRADE")
                result = await self._execute_standard_trade(client, opportunity)

            # Update execution statistics
            self._update_execution_stats(opportunity, result)

            return result

        except Exception as e:
            logger.error(f"❌ [EXECUTE] Error executing opportunity: {e}")
            return {"error": str(e), "success": False}

    async def _validate_execution(self, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Validate that an opportunity can be executed"""
        try:
            exchange_name = opportunity.pair.exchange
            client = self.exchange_clients[exchange_name]

            # Check if we have sufficient balance
            if opportunity.side == 'buy':
                # Need quote currency balance
                required_currency = opportunity.pair.quote
                required_amount = float(opportunity.amount * opportunity.price)
            else:
                # Need base currency balance
                required_currency = opportunity.pair.base
                required_amount = float(opportunity.amount)

            # Get current balance
            if hasattr(client, 'get_balance'):
                current_balance = await client.get_balance(required_currency)
                if current_balance is None or float(current_balance) < required_amount:
                    return {
                        'valid': False,
                        'reason': f"Insufficient {required_currency} balance: {current_balance} < {required_amount}"
                    }

            # Check minimum order value
            order_value = opportunity.amount * opportunity.price
            if order_value < opportunity.pair.min_order_value:
                return {
                    'valid': False,
                    'reason': f"Order value {order_value} below minimum {opportunity.pair.min_order_value}"
                }

            return {'valid': True, 'reason': 'Validation passed'}

        except Exception as e:
            return {'valid': False, 'reason': f"Validation error: {str(e)}"}

    async def _execute_direct_trade(self, client, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Execute a direct currency trade"""
        try:
            if hasattr(client, 'place_order'):
                result = await client.place_order(
                    symbol=opportunity.pair.symbol,
                    side=opportunity.side,
                    amount=float(opportunity.amount),
                    order_type='market'
                )

                if 'error' not in result:
                    logger.info(f"✅ [DIRECT-TRADE] Successfully executed {opportunity.side} order")
                    return {"success": True, "order_id": result.get('order_id'), "result": result}
                else:
                    logger.error(f"❌ [DIRECT-TRADE] Order failed: {result['error']}")
                    return {"success": False, "error": result['error']}
            else:
                return {"success": False, "error": "Client does not support place_order"}

        except Exception as e:
            logger.error(f"❌ [DIRECT-TRADE] Execution error: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_arbitrage_trade(self, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Execute cross-exchange arbitrage trade"""
        try:
            # This would involve buying on one exchange and selling on another
            # For now, implement as a direct trade on the cheaper exchange
            exchange_name = opportunity.pair.exchange
            client = self.exchange_clients[exchange_name]

            return await self._execute_direct_trade(client, opportunity)

        except Exception as e:
            logger.error(f"❌ [ARBITRAGE-TRADE] Execution error: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_triangular_arbitrage(self, client, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Execute triangular arbitrage trade"""
        try:
            # This would involve a sequence of three trades
            # For now, implement as a direct trade
            return await self._execute_direct_trade(client, opportunity)

        except Exception as e:
            logger.error(f"❌ [TRIANGULAR-TRADE] Execution error: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_rebalancing_trade(self, client, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Execute portfolio rebalancing trade"""
        try:
            return await self._execute_direct_trade(client, opportunity)

        except Exception as e:
            logger.error(f"❌ [REBALANCING-TRADE] Execution error: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_standard_trade(self, client, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Execute standard trade"""
        try:
            return await self._execute_direct_trade(client, opportunity)

        except Exception as e:
            logger.error(f"❌ [STANDARD-TRADE] Execution error: {e}")
            return {"success": False, "error": str(e)}

    def _update_execution_stats(self, opportunity: TradingOpportunity, result: Dict[str, Any]):
        """Update execution statistics"""
        try:
            strategy = opportunity.strategy
            if strategy not in self.execution_stats:
                self.execution_stats[strategy] = {
                    'total_executions': 0,
                    'successful_executions': 0,
                    'failed_executions': 0,
                    'total_profit': Decimal('0'),
                    'success_rate': 0.0
                }

            stats = self.execution_stats[strategy]
            stats['total_executions'] += 1

            if result.get('success', False):
                stats['successful_executions'] += 1
                stats['total_profit'] += opportunity.expected_profit
            else:
                stats['failed_executions'] += 1

            stats['success_rate'] = stats['successful_executions'] / stats['total_executions']

            logger.info(f"📊 [STATS] {strategy}: {stats['success_rate']:.2%} success rate")

        except Exception as e:
            logger.error(f"❌ [STATS] Error updating execution stats: {e}")

    async def run_continuous_trading(self):
        """Run continuous multi-currency trading"""
        try:
            logger.info("🚀 [CONTINUOUS] Starting continuous multi-currency trading...")

            while True:
                try:
                    # Find trading opportunities
                    opportunities = await self.find_trading_opportunities()

                    if opportunities:
                        # Execute the best opportunity
                        best_opportunity = opportunities[0]
                        result = await self.execute_trading_opportunity(best_opportunity)

                        if result.get('success', False):
                            logger.info(f"✅ [CONTINUOUS] Successfully executed {best_opportunity.strategy}")

                            # APPLY COOLDOWNS AFTER SUCCESSFUL TRADE
                            pair_symbol = f"{best_opportunity.pair.base}{best_opportunity.pair.quote}"
                            self._add_pair_cooldown(pair_symbol)
                            self._add_strategy_cooldown(best_opportunity.strategy)

                            # Record trade for strategy balancing
                            self._record_trade_for_strategy_balancing(best_opportunity, result)

                            # Reset consecutive count for other pairs (successful diversification)
                            for pair in self.consecutive_pair_count:
                                if pair != pair_symbol:
                                    self.consecutive_pair_count[pair] = max(0, self.consecutive_pair_count[pair] - 1)

                            logger.info(f"✅ [DIVERSIFICATION] Applied cooldowns and updated tracking for {pair_symbol}")
                        else:
                            logger.warning(f"⚠️ [CONTINUOUS] Failed to execute opportunity: {result.get('error')}")
                    else:
                        logger.info("🔍 [CONTINUOUS] No trading opportunities found")

                    # CRITICAL FIX: Immediate execution - no delays for aggressive micro-trading
                    # Only yield control briefly to prevent blocking
                    await asyncio.sleep(0.1)  # 100ms minimal yield

                except Exception as e:
                    logger.error(f"❌ [CONTINUOUS] Error in trading loop: {e}")
                    # CRITICAL FIX: Don't halt trading on single failures - continue immediately
                    # Only brief pause to prevent error spam
                    await asyncio.sleep(1.0)  # 1 second minimal pause on error

        except Exception as e:
            logger.error(f"❌ [CONTINUOUS] Critical error in continuous trading: {e}")
            raise

    async def initialize_balance_manager(self):
        """Initialize the balance-aware order manager"""
        try:
            if BalanceAwareOrderManager is None:
                logger.warning("⚠️ [BALANCE-MANAGER] BalanceAwareOrderManager not available")
                return

            logger.info("⚖️ [BALANCE-MANAGER] Initializing balance-aware order manager...")

            self.balance_manager = BalanceAwareOrderManager(
                exchange_clients=self.exchange_clients,
                config={
                    'balance_buffer': 0.05,           # 5% balance buffer
                    'min_order_value': 5.0,          # $5 minimum order
                    'max_balance_usage': 0.9,        # 90% max balance usage
                    'critical_threshold': 0.1,       # 10% critical threshold
                    'sizing_strategy': 'dynamic',    # Dynamic order sizing
                    'aggressive_trading': True,      # Enable aggressive trading
                    'micro_trading': True,           # Enable micro-trading
                    'balance_percentage': 0.85       # 85% balance usage for percentage strategy
                }
            )

            # Initialize with timeout to prevent hanging
            try:
                await asyncio.wait_for(self.balance_manager.initialize(), timeout=30.0)
            except asyncio.TimeoutError:
                logger.warning("⚠️ [BALANCE-MANAGER] Initialization timed out, continuing without full initialization")

            logger.info("✅ [BALANCE-MANAGER] Balance-aware order manager initialized")

        except Exception as e:
            logger.error(f"❌ [BALANCE-MANAGER] Error initializing balance manager: {e}")

    async def execute_with_balance_validation(self, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Execute trading opportunity with comprehensive balance validation"""
        try:
            if not self.balance_manager:
                # Fall back to standard execution if balance manager not available
                return await self.execute_trading_opportunity(opportunity)

            logger.info(f"⚖️ [BALANCE-EXEC] Executing with balance validation")
            logger.info(f"⚖️ [BALANCE-EXEC] {opportunity.side} {opportunity.amount} {opportunity.pair.symbol}")

            # Execute using balance-aware order manager
            result = await self.balance_manager.execute_balance_aware_order(
                symbol=opportunity.pair.symbol,
                side=opportunity.side,
                amount=opportunity.amount,
                exchange=opportunity.pair.exchange,
                order_type="market"
            )

            if result.get("success", False):
                logger.info("✅ [BALANCE-EXEC] Balance-validated order executed successfully")

                # Update execution statistics
                self._update_execution_stats(opportunity, result)

                return result
            else:
                logger.warning(f"⚠️ [BALANCE-EXEC] Balance-validated order failed: {result.get('error')}")

                # REMOVED: Fallback trading strategies - system must maintain full sophistication
                logger.error("❌ [SYSTEM-INTEGRITY] Fallback trading strategies disabled")
                logger.error("❌ [SYSTEM-INTEGRITY] Balance validation failed - system fails gracefully rather than degrade")

                    if fallback_result.get('success', False):
                        logger.info(f"✅ [FALLBACK] Fallback strategy successful: {fallback_result.get('fallback_strategy')}")
                        fallback_result['original_opportunity'] = {
                            'symbol': opportunity.pair.symbol,
                            'side': opportunity.side,
                            'amount': float(opportunity.amount)
                        }
                        return fallback_result
                    else:
                        logger.warning(f"⚠️ [FALLBACK] Fallback strategy failed: {fallback_result.get('error')}")

                # Check if alternatives are available (fallback to legacy logic)
                if 'alternatives' in result and result['alternatives']:
                    logger.info(f"🔄 [BALANCE-EXEC] Alternative currencies available: {result['alternatives']}")

                    # Try to find alternative trading opportunity
                    alternative_result = await self._try_alternative_currencies(opportunity, result['alternatives'])
                    if alternative_result.get("success", False):
                        return alternative_result

                return result

        except Exception as e:
            logger.error(f"❌ [BALANCE-EXEC] Error in balance-validated execution: {e}")
            return {"success": False, "error": str(e)}

    async def _try_alternative_currencies(self, opportunity: TradingOpportunity,
                                        alternatives: List[str]) -> Dict[str, Any]:
        """Try to execute trade using alternative currencies"""
        try:
            logger.info(f"🔄 [ALTERNATIVES] Trying alternative currencies: {alternatives}")

            for alt_currency in alternatives:
                try:
                    # Create alternative trading opportunity
                    alt_symbol = f"{alt_currency}USDT"

                    # Check if this pair exists in our active pairs
                    exchange = opportunity.pair.exchange
                    if exchange in self.active_pairs and alt_symbol in self.active_pairs[exchange]:
                        alt_pair = self.active_pairs[exchange][alt_symbol]

                        # Create alternative opportunity
                        alt_opportunity = TradingOpportunity(
                            pair=alt_pair,
                            side=opportunity.side,
                            amount=opportunity.amount,  # Will be adjusted by balance manager
                            price=Decimal('0'),  # Will be determined by market
                            expected_profit=opportunity.expected_profit,
                            confidence=opportunity.confidence * 0.8,  # Slightly lower confidence
                            strategy=f"alternative_{opportunity.strategy}",
                            risk_score=opportunity.risk_score * 1.1,  # Slightly higher risk
                            execution_priority=opportunity.execution_priority + 1
                        )

                        # Try to execute alternative
                        result = await self.balance_manager.execute_balance_aware_order(
                            symbol=alt_symbol,
                            side=opportunity.side,
                            amount=opportunity.amount,
                            exchange=exchange,
                            order_type="market"
                        )

                        if result.get("success", False):
                            logger.info(f"✅ [ALTERNATIVES] Successfully executed with {alt_currency}")
                            result['alternative_currency'] = alt_currency
                            result['original_symbol'] = opportunity.pair.symbol
                            return result
                        else:
                            logger.info(f"⚠️ [ALTERNATIVES] {alt_currency} also failed: {result.get('error')}")
                            continue

                except Exception as e:
                    logger.warning(f"⚠️ [ALTERNATIVES] Error trying {alt_currency}: {e}")
                    continue

            logger.warning("⚠️ [ALTERNATIVES] All alternative currencies failed")
            return {"success": False, "error": "All alternative currencies failed"}

        except Exception as e:
            logger.error(f"❌ [ALTERNATIVES] Error trying alternatives: {e}")
            return {"success": False, "error": str(e)}

    async def initialize_capital_manager(self):
        """Initialize the cross-exchange capital manager"""
        try:
            if CrossExchangeCapitalManager is None:
                logger.warning("⚠️ [CAPITAL-MANAGER] CrossExchangeCapitalManager not available")
                return

            logger.info("🏦 [CAPITAL-MANAGER] Initializing cross-exchange capital manager...")

            self.capital_manager = CrossExchangeCapitalManager(
                exchange_clients=self.exchange_clients,
                config={
                    'coinbase_wallet_address': '******************************************',
                    'min_transfer_amount': Decimal('10'),    # $10 minimum transfer
                    'max_transfer_amount': Decimal('1000'),  # $1000 maximum transfer
                    'emergency_threshold': Decimal('5'),    # $5 emergency threshold
                    'max_gas_cost_percentage': 0.05,        # 5% max gas cost
                    'preferred_networks': ['polygon', 'bsc', 'arbitrum', 'ethereum']
                }
            )

            await self.capital_manager.initialize()

            logger.info("✅ [CAPITAL-MANAGER] Cross-exchange capital manager initialized")

        except Exception as e:
            logger.error(f"❌ [CAPITAL-MANAGER] Error initializing capital manager: {e}")

    async def initialize_currency_switcher(self):
        """Initialize the intelligent currency switcher"""
        try:
            if IntelligentCurrencySwitcher is None:
                logger.warning("⚠️ [CURRENCY-SWITCHER] IntelligentCurrencySwitcher not available")
                return

            logger.info("🔄 [CURRENCY-SWITCHER] Initializing intelligent currency switcher...")

            # Use the primary exchange client for currency switching
            primary_exchange = None
            primary_client = None

            # Prefer Bybit for currency switching due to lower fees and better liquidity
            if 'bybit' in self.exchange_clients:
                primary_exchange = 'bybit'
                primary_client = self.exchange_clients['bybit']
            elif 'coinbase' in self.exchange_clients:
                primary_exchange = 'coinbase'
                primary_client = self.exchange_clients['coinbase']
            else:
                # Use the first available exchange
                primary_exchange = list(self.exchange_clients.keys())[0]
                primary_client = list(self.exchange_clients.values())[0]

            if primary_client:
                self.currency_switcher = IntelligentCurrencySwitcher(
                    exchange_client=primary_client,
                    min_thresholds={
                        'USDT': 5.0,    # Bybit minimum
                        'USD': 1.0,     # Coinbase minimum
                        'BTC': 0.0001,  # Minimum BTC amount
                        'ETH': 0.001,   # Minimum ETH amount
                        'SOL': 0.01,    # Minimum SOL amount
                        'ADA': 1.0,     # Minimum ADA amount
                        'DOT': 0.1,     # Minimum DOT amount
                        'LINK': 0.1,    # Minimum LINK amount
                        'UNI': 0.1,     # Minimum UNI amount
                        'AVAX': 0.1,    # Minimum AVAX amount
                        'MATIC': 1.0,   # Minimum MATIC amount
                    }
                )

                logger.info(f"✅ [CURRENCY-SWITCHER] Initialized with {primary_exchange} exchange")
            else:
                logger.warning("⚠️ [CURRENCY-SWITCHER] No exchange clients available")

        except Exception as e:
            logger.error(f"❌ [CURRENCY-SWITCHER] Error initializing currency switcher: {e}")

    async def handle_insufficient_capital(self, exchange_name: str) -> Dict[str, Any]:
        """Handle insufficient capital situation by triggering cross-exchange transfers"""
        try:
            if not self.capital_manager:
                return {"success": False, "error": "Capital manager not available"}

            logger.warning(f"🚨 [CAPITAL] Insufficient capital detected on {exchange_name}")

            # Assess capital needs
            assessment = await self.capital_manager.assess_capital_needs(exchange_name)

            if 'error' in assessment:
                return {"success": False, "error": assessment['error']}

            # Execute recommended transfers
            transfer_results = []

            for transfer_rec in assessment.get('recommended_transfers', []):
                try:
                    # Create transfer request
                    transfer_request = TransferRequest(
                        transfer_id=f"auto_{int(time.time())}_{transfer_rec['currency']}",
                        transfer_type=TransferType.DEPOSIT,
                        currency=transfer_rec['currency'],
                        amount=transfer_rec['amount'],
                        source_exchange=transfer_rec['source'],
                        target_exchange=transfer_rec['target'],
                        priority=90 if transfer_rec['priority'] == 'high' else 70,
                        reason=f"Insufficient {transfer_rec['currency']} on {exchange_name}",
                        estimated_cost=transfer_rec['estimated_cost'],
                        estimated_time=1800,  # 30 minutes default
                        gas_cost=transfer_rec['estimated_cost'],
                        network='polygon'  # Default to Polygon for lower fees
                    )

                    # Execute transfer
                    result = await self.capital_manager.execute_capital_transfer(transfer_request)
                    transfer_results.append(result)

                    if result.get('success', False):
                        logger.info(f"✅ [CAPITAL] Transfer initiated: {transfer_rec['currency']}")
                    else:
                        logger.error(f"❌ [CAPITAL] Transfer failed: {result.get('error')}")

                except Exception as e:
                    logger.error(f"❌ [CAPITAL] Error executing transfer: {e}")
                    continue

            successful_transfers = sum(1 for r in transfer_results if r.get('success', False))

            return {
                "success": successful_transfers > 0,
                "assessment": assessment,
                "transfers_initiated": successful_transfers,
                "total_transfers": len(transfer_results),
                "transfer_results": transfer_results
            }

        except Exception as e:
            logger.error(f"❌ [CAPITAL] Error handling insufficient capital: {e}")
            return {"success": False, "error": str(e)}

    async def monitor_capital_across_exchanges(self):
        """Monitor capital levels across all exchanges and trigger transfers when needed"""
        try:
            if not self.capital_manager:
                return

            logger.info("🔍 [CAPITAL-MONITOR] Monitoring capital across exchanges...")

            # Update all exchange balances
            await self.capital_manager.update_all_exchange_balances()

            # Check each exchange for capital needs
            for exchange_name in self.exchange_clients.keys():
                if exchange_name == 'coinbase':  # Skip Coinbase as it's the capital source
                    continue

                try:
                    assessment = await self.capital_manager.assess_capital_needs(exchange_name)

                    if assessment.get('needs_emergency', False):
                        logger.warning(f"🚨 [CAPITAL-MONITOR] Emergency capital needed on {exchange_name}")
                        await self.handle_insufficient_capital(exchange_name)
                    elif assessment.get('recommended_transfers'):
                        logger.info(f"💡 [CAPITAL-MONITOR] {exchange_name} could benefit from capital transfers")

                except Exception as e:
                    logger.error(f"❌ [CAPITAL-MONITOR] Error monitoring {exchange_name}: {e}")
                    continue

        except Exception as e:
            logger.error(f"❌ [CAPITAL-MONITOR] Error in capital monitoring: {e}")

    async def get_capital_status_report(self) -> Dict[str, Any]:
        """Generate comprehensive capital status report"""
        try:
            if not self.capital_manager:
                return {"error": "Capital manager not available"}

            # Update balances
            await self.capital_manager.update_all_exchange_balances()

            report = {
                'timestamp': time.time(),
                'exchanges': {},
                'total_capital_usd': Decimal('0'),
                'capital_distribution': {},
                'transfer_statistics': self.capital_manager.transfer_stats.copy(),
                'recommendations': []
            }

            # Analyze each exchange
            for exchange_name, balances in self.capital_manager.exchange_balances.items():
                exchange_total = sum(balance.usd_value for balance in balances.values())

                report['exchanges'][exchange_name] = {
                    'total_usd_value': float(exchange_total),
                    'currency_count': len(balances),
                    'balances': {
                        currency: {
                            'amount': float(balance.available),
                            'usd_value': float(balance.usd_value)
                        }
                        for currency, balance in balances.items()
                    }
                }

                report['total_capital_usd'] += exchange_total
                report['capital_distribution'][exchange_name] = float(exchange_total)

            # Generate recommendations
            total_capital = float(report['total_capital_usd'])
            if total_capital < 100:  # Less than $100 total
                report['recommendations'].append("Consider increasing total capital for better trading opportunities")

            # Check distribution
            if 'coinbase' in report['capital_distribution']:
                coinbase_percentage = report['capital_distribution']['coinbase'] / max(1, total_capital)
                if coinbase_percentage < 0.5:  # Less than 50% on Coinbase
                    report['recommendations'].append("Consider maintaining more capital on Coinbase for transfers")

            if not report['recommendations']:
                report['recommendations'].append("Capital distribution looks healthy")

            return report

        except Exception as e:
            logger.error(f"❌ [CAPITAL-REPORT] Error generating capital report: {e}")
            return {'error': str(e)}

    async def initialize_error_recovery(self):
        """Initialize the robust error recovery system"""
        try:
            if RobustErrorRecovery is None:
                logger.warning("⚠️ [ERROR-RECOVERY] RobustErrorRecovery not available")
                return

            logger.info("🛡️ [ERROR-RECOVERY] Initializing robust error recovery system...")

            self.error_recovery = RobustErrorRecovery(
                trading_engine=self,
                config={
                    'max_consecutive_errors': 10,      # Max 10 consecutive errors
                    'error_cooldown_period': 300,     # 5 minute cooldown
                    'emergency_stop_threshold': 20,   # Emergency stop after 20 errors
                }
            )

            logger.info("✅ [ERROR-RECOVERY] Robust error recovery system initialized")

        except Exception as e:
            logger.error(f"❌ [ERROR-RECOVERY] Error initializing error recovery: {e}")

    async def execute_with_error_recovery(self, operation_func: callable, operation_name: str, **kwargs) -> Dict[str, Any]:
        """Execute an operation with comprehensive error recovery"""
        try:
            if not self.error_recovery:
                # Fallback to direct execution if error recovery not available
                return await operation_func(**kwargs)

            # Check circuit breaker
            if self.error_recovery.is_circuit_breaker_active():
                logger.warning("🔴 [ERROR-RECOVERY] Circuit breaker active - skipping operation")
                return {"success": False, "error": "Circuit breaker active"}

            max_attempts = 3
            attempt = 0

            while attempt < max_attempts:
                try:
                    # Execute the operation
                    result = await operation_func(**kwargs)

                    if result.get('success', False):
                        return result
                    else:
                        # Operation failed, but no exception - treat as error
                        error_msg = result.get('error', 'Operation failed')
                        raise Exception(error_msg)

                except Exception as e:
                    attempt += 1

                    # Handle error with recovery system
                    recovery_success = await self.error_recovery.handle_error(
                        error=e,
                        function_name=operation_name,
                        retry_count=attempt,
                        **kwargs
                    )

                    if not recovery_success or attempt >= max_attempts:
                        logger.error(f"❌ [ERROR-RECOVERY] Failed to recover from error in {operation_name}")
                        return {"success": False, "error": str(e), "attempts": attempt}

                    # Recovery successful, try again
                    logger.info(f"🔧 [ERROR-RECOVERY] Recovery successful, retrying {operation_name} (attempt {attempt + 1})")
                    # CRITICAL FIX: Immediate retry for aggressive trading - no delays
                    await asyncio.sleep(0.1)  # Minimal 100ms delay before retry

            return {"success": False, "error": "Max attempts exceeded", "attempts": max_attempts}

        except Exception as e:
            logger.error(f"❌ [ERROR-RECOVERY] Critical error in error recovery system: {e}")
            return {"success": False, "error": str(e)}

    async def run_continuous_trading_with_recovery(self):
        """Run continuous trading with comprehensive error recovery"""
        try:
            logger.info("🚀 [CONTINUOUS-RECOVERY] Starting continuous trading with error recovery...")

            consecutive_failures = 0
            max_consecutive_failures = 5

            while True:
                try:
                    # Check circuit breaker
                    if self.error_recovery and self.error_recovery.is_circuit_breaker_active():
                        logger.warning("🔴 [CONTINUOUS-RECOVERY] Circuit breaker active - brief pause...")
                        # CRITICAL FIX: Reduce circuit breaker delay for aggressive trading
                        await asyncio.sleep(5)  # 5 second pause instead of 1 minute
                        continue

                    # Execute trading cycle with error recovery
                    cycle_result = await self.execute_with_error_recovery(
                        operation_func=self._execute_trading_cycle,
                        operation_name="trading_cycle"
                    )

                    if cycle_result.get('success', False):
                        consecutive_failures = 0  # Reset failure counter
                        logger.info("✅ [CONTINUOUS-RECOVERY] Trading cycle completed successfully")
                    else:
                        consecutive_failures += 1
                        logger.warning(f"⚠️ [CONTINUOUS-RECOVERY] Trading cycle failed ({consecutive_failures}/{max_consecutive_failures})")

                        if consecutive_failures >= max_consecutive_failures:
                            logger.error("🚨 [CONTINUOUS-RECOVERY] Too many consecutive failures - brief cooldown")
                            # CRITICAL FIX: Reduce cooldown for aggressive trading
                            await asyncio.sleep(10)  # 10 second cooldown instead of 5 minutes
                            consecutive_failures = 0

                    # CRITICAL FIX: Immediate execution - minimal delay between cycles
                    await asyncio.sleep(0.5)  # 500ms intervals for aggressive micro-trading

                except KeyboardInterrupt:
                    logger.info("🛑 [CONTINUOUS-RECOVERY] Received interrupt signal")
                    break
                except Exception as e:
                    logger.error(f"❌ [CONTINUOUS-RECOVERY] Critical error in continuous trading: {e}")
                    consecutive_failures += 1
                    # CRITICAL FIX: Minimal delay on critical error for aggressive trading
                    await asyncio.sleep(2)  # 2 second pause instead of 1 minute

            logger.info("🛑 [CONTINUOUS-RECOVERY] Continuous trading with recovery stopped")

        except Exception as e:
            logger.error(f"❌ [CONTINUOUS-RECOVERY] Fatal error in continuous trading: {e}")
            raise

    async def _execute_trading_cycle(self) -> Dict[str, Any]:
        """Execute a single trading cycle with PRIORITY for direct trading opportunities"""
        try:
            # Step 1: Scan for opportunities with timeout protection
            opportunities = await self.find_trading_opportunities()

            if not opportunities:
                # FALLBACK: Try to find direct opportunities only if main scan failed
                logger.warning("🔄 [TRADING-CYCLE] No opportunities found - trying direct opportunities only")
                try:
                    direct_opportunities = await asyncio.wait_for(
                        self._find_direct_trading_opportunities(),
                        timeout=15  # 15 second timeout for direct opportunities
                    )
                    if direct_opportunities:
                        opportunities = direct_opportunities
                        logger.info(f"🎯 [TRADING-CYCLE] Found {len(direct_opportunities)} direct opportunities as fallback")
                except Exception as e:
                    logger.warning(f"⚠️ [TRADING-CYCLE] Fallback direct opportunities failed: {e}")

                if not opportunities:
                    return {"success": True, "message": "No opportunities found", "opportunities": 0}

            # Step 2: INTELLIGENT OPPORTUNITY SELECTION WITH DIVERSIFICATION
            # Group opportunities by strategy for balanced selection
            strategy_groups = {}
            for opp in opportunities:
                if opp.strategy not in strategy_groups:
                    strategy_groups[opp.strategy] = []
                strategy_groups[opp.strategy].append(opp)

            logger.info(f"🎯 [STRATEGY-GROUPS] Found {len(strategy_groups)} strategy types: {list(strategy_groups.keys())}")

            # Select strategy based on rotation and performance
            best_opportunity = None

            # ENHANCED: Dynamic strategy priority based on diversification needs
            strategy_priority = await self._get_dynamic_strategy_priority(strategy_groups)

            # Find the best opportunity from available strategies with diversification awareness
            best_opportunity = None
            for strategy in strategy_priority:
                if strategy in strategy_groups and strategy_groups[strategy]:
                    # Sort opportunities within strategy by diversification score and profit potential
                    sorted_opps = sorted(strategy_groups[strategy],
                                       key=lambda x: (self._calculate_diversification_score(x), x.confidence, x.expected_profit),
                                       reverse=True)

                    # Select the best diversified opportunity from this strategy
                    best_opportunity = sorted_opps[0]
                    pair_symbol = f"{best_opportunity.pair.base}{best_opportunity.pair.quote}"
                    logger.info(f"🎯 [STRATEGY-SELECTION] Selected {strategy} for {pair_symbol} with confidence {best_opportunity.confidence:.3f}")
                    break

            # ENHANCED: Intelligent fallback with strategy balancing
            if not best_opportunity and opportunities:
                # Try to balance strategy usage
                strategy_usage = self._get_recent_strategy_usage()
                underused_strategies = self._find_underused_strategies(strategy_usage, strategy_groups)

                if underused_strategies:
                    # Prioritize underused strategies
                    for strategy in underused_strategies:
                        if strategy in strategy_groups and strategy_groups[strategy]:
                            sorted_opps = sorted(strategy_groups[strategy],
                                               key=lambda x: (self._calculate_diversification_score(x), x.confidence, x.expected_profit),
                                               reverse=True)
                            best_opportunity = sorted_opps[0]
                            logger.info(f"🎯 [BALANCE-SELECTION] Selected underused strategy {strategy}")
                            break

                # Final fallback: best overall opportunity
                if not best_opportunity:
                    best_opportunity = max(opportunities,
                                         key=lambda x: (self._calculate_diversification_score(x), x.confidence, x.expected_profit))
                    logger.info(f"🎯 [FALLBACK-SELECTION] Selected {best_opportunity.strategy} as final fallback")

            # Step 3: Execute with balance validation
            if self.balance_manager:
                result = await self.execute_with_balance_validation(best_opportunity)
            else:
                result = await self.execute_trading_opportunity(best_opportunity)

            return {
                "success": result.get('success', False),
                "opportunities": len(opportunities),
                "executed_strategy": best_opportunity.strategy,
                "result": result
            }

        except Exception as e:
            logger.error(f"❌ [TRADING-CYCLE] Error in trading cycle: {e}")
            return {"success": False, "error": str(e)}

    async def get_system_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive system health report including error recovery status"""
        try:
            report = {
                'timestamp': time.time(),
                'system_status': 'operational',
                'components': {},
                'error_recovery': {},
                'recommendations': []
            }

            # Get base system status
            base_report = await self.get_comprehensive_status_report()
            if 'error' not in base_report:
                report.update(base_report)

            # Add error recovery status
            if self.error_recovery:
                error_report = self.error_recovery.get_error_recovery_report()
                report['error_recovery'] = error_report

                # Check error recovery health
                if error_report.get('circuit_breaker_active', False):
                    report['system_status'] = 'degraded'
                    report['recommendations'].append("Circuit breaker active - system in recovery mode")

                success_rate = error_report.get('recovery_success_rate', 0)
                if success_rate < 80:  # Less than 80% recovery success
                    report['recommendations'].append(f"Low error recovery success rate: {success_rate:.1f}%")

                recent_errors = error_report.get('total_errors_last_hour', 0)
                if recent_errors > 10:  # More than 10 errors in last hour
                    report['recommendations'].append(f"High error rate: {recent_errors} errors in last hour")

            # Add capital management status
            if self.capital_manager:
                capital_report = await self.get_capital_status_report()
                if 'error' not in capital_report:
                    report['capital_management'] = capital_report

            # Overall health assessment
            if report['system_status'] == 'operational' and not report['recommendations']:
                report['recommendations'].append("All systems operating normally")

            return report

        except Exception as e:
            logger.error(f"❌ [HEALTH-REPORT] Error generating health report: {e}")
            return {'error': str(e), 'timestamp': time.time()}

    def _is_pair_on_cooldown(self, pair: str) -> bool:
        """Check if a trading pair is on cooldown"""
        if pair not in self.pair_cooldowns:
            return False

        cooldown_end = self.pair_cooldowns[pair]
        current_time = time.time()

        if current_time >= cooldown_end:
            # Cooldown expired, remove from tracking
            del self.pair_cooldowns[pair]
            return False

        return True

    def _is_strategy_on_cooldown(self, strategy: str) -> bool:
        """Check if a strategy is on cooldown"""
        if strategy not in self.strategy_cooldowns:
            return False

        cooldown_end = self.strategy_cooldowns[strategy]
        current_time = time.time()

        if current_time >= cooldown_end:
            # Cooldown expired, remove from tracking
            del self.strategy_cooldowns[strategy]
            return False

        return True

    def _add_pair_cooldown(self, pair: str):
        """Enhanced pair cooldown with aggressive diversification tracking"""
        current_time = time.time()
        self.pair_cooldowns[pair] = current_time + self.pair_cooldown_duration

        # Track consecutive trades on same pair
        if pair not in self.consecutive_pair_count:
            self.consecutive_pair_count[pair] = 0
        self.consecutive_pair_count[pair] += 1

        # Add to recent pairs list
        if pair in self.last_traded_pairs:
            self.last_traded_pairs.remove(pair)
        self.last_traded_pairs.insert(0, pair)

        # Keep only recent pairs
        if len(self.last_traded_pairs) > self.max_recent_pairs:
            self.last_traded_pairs = self.last_traded_pairs[:self.max_recent_pairs]

        # ENHANCED: Reset consecutive count for other pairs to encourage diversification
        for other_pair in self.consecutive_pair_count:
            if other_pair != pair:
                self.consecutive_pair_count[other_pair] = max(0, self.consecutive_pair_count[other_pair] - 1)

        # Update rotation index for systematic diversification
        self.pair_rotation_index = (self.pair_rotation_index + 1) % 10

        logger.info(f"🔄 [PAIR-COOLDOWN] {pair} added to cooldown for {self.pair_cooldown_duration}s")
        logger.info(f"🔄 [PAIR-TRACKING] Consecutive trades on {pair}: {self.consecutive_pair_count[pair]}")
        logger.info(f"🔄 [DIVERSIFICATION] Recent pairs: {self.last_traded_pairs[:5]}")
        logger.info(f"🔄 [ROTATION] Rotation index: {self.pair_rotation_index}")

    def _add_strategy_cooldown(self, strategy: str):
        """Add a strategy to cooldown"""
        current_time = time.time()
        self.strategy_cooldowns[strategy] = current_time + self.strategy_cooldown_duration
        logger.info(f"🔄 [STRATEGY-COOLDOWN] {strategy} added to cooldown for {self.strategy_cooldown_duration}s")

    def _should_force_diversification(self, pair: str) -> bool:
        """Enhanced diversification enforcement - force rotation across multiple pairs"""
        # ENHANCED: More aggressive diversification enforcement

        # 1. Check consecutive trades (now max 1)
        consecutive_count = self.consecutive_pair_count.get(pair, 0)
        if consecutive_count >= self.max_consecutive_same_pair:
            logger.info(f"🔄 [FORCE-DIVERSIFICATION] {pair} hit max consecutive trades ({consecutive_count})")
            return True

        # 2. Check if pair was traded in last 3 trades (force rotation)
        recent_pairs = self.last_traded_pairs[:3]
        if pair in recent_pairs:
            logger.info(f"🔄 [FORCE-DIVERSIFICATION] {pair} traded recently - forcing rotation")
            return True

        # 3. Systematic rotation enforcement
        if self.diversification_enforcement and len(self.last_traded_pairs) > 0:
            last_pair = self.last_traded_pairs[0]
            if pair == last_pair:
                logger.info(f"🔄 [FORCE-DIVERSIFICATION] {pair} was last traded - enforcing rotation")
                return True

        # Check if pair appears too frequently in recent trades
        recent_frequency = self.last_traded_pairs[:5].count(pair)  # Last 5 trades
        if recent_frequency >= 3:  # More than 60% of recent trades
            logger.info(f"🔄 [FORCE-DIVERSIFICATION] {pair} too frequent in recent trades ({recent_frequency}/5)")
            return True

        return False

    async def _get_dynamic_strategy_priority(self, strategy_groups: Dict[str, List]) -> List[str]:
        """Get dynamic strategy priority based on current market conditions and diversification needs"""
        try:
            # Base priority order
            base_priority = [
                'triangular_arbitrage',      # Highest profit potential
                'cross_exchange_arbitrage',  # Cross-exchange opportunities
                'portfolio_rebalancing',     # Portfolio optimization
                'direct_currency_trading'    # Most reliable fallback
            ]

            # Adjust priority based on recent trading patterns
            recent_strategies = [trade.get('strategy', '') for trade in self.last_traded_pairs[-5:]]
            strategy_frequency = {}
            for strategy in recent_strategies:
                strategy_frequency[strategy] = strategy_frequency.get(strategy, 0) + 1

            # Deprioritize overused strategies
            adjusted_priority = []
            for strategy in base_priority:
                if strategy in strategy_groups:
                    frequency = strategy_frequency.get(strategy, 0)
                    # If strategy was used more than 60% of recent trades, lower its priority
                    if frequency <= 3:  # 3 out of 5 recent trades
                        adjusted_priority.append(strategy)

            # Add back overused strategies at the end
            for strategy in base_priority:
                if strategy not in adjusted_priority and strategy in strategy_groups:
                    adjusted_priority.append(strategy)

            logger.info(f"🎯 [DYNAMIC-PRIORITY] Strategy priority: {adjusted_priority}")
            return adjusted_priority

        except Exception as e:
            logger.error(f"❌ [DYNAMIC-PRIORITY] Error calculating dynamic priority: {e}")
            return ['triangular_arbitrage', 'cross_exchange_arbitrage', 'portfolio_rebalancing', 'direct_currency_trading']

    def _calculate_diversification_score(self, opportunity) -> float:
        """Calculate diversification score for an opportunity (higher = more diversified)"""
        try:
            pair_symbol = f"{opportunity.pair.base}{opportunity.pair.quote}"

            # Base score
            score = 1.0

            # Bonus for currencies we don't currently hold much of
            current_holdings = getattr(self, 'current_holdings_count', {})
            base_currency_count = current_holdings.get(opportunity.pair.base, 0)

            # Higher score for less-held currencies
            if base_currency_count == 0:
                score += 0.5  # Big bonus for new currencies
            elif base_currency_count <= 2:
                score += 0.3  # Medium bonus for lightly held currencies
            elif base_currency_count >= 5:
                score -= 0.2  # Penalty for heavily held currencies

            # Bonus for pairs not recently traded
            recent_pair_count = self.last_traded_pairs[:10].count(pair_symbol)
            if recent_pair_count == 0:
                score += 0.4  # Big bonus for never recently traded
            elif recent_pair_count <= 2:
                score += 0.2  # Medium bonus for lightly traded
            else:
                score -= 0.3  # Penalty for frequently traded

            # Bonus for non-BTC pairs to encourage diversification
            if opportunity.pair.base != 'BTC':
                score += 0.1

            return max(0.1, score)  # Ensure minimum score

        except Exception as e:
            logger.error(f"❌ [DIVERSIFICATION-SCORE] Error calculating score: {e}")
            return 1.0

    def _update_holdings_count(self):
        """Update current holdings count for diversification scoring"""
        try:
            self.current_holdings_count = {}

            for exchange_name, balances in self.currency_balances.items():
                for currency, balance in balances.items():
                    if balance > 0 and currency != 'USDT' and currency != 'USD':
                        self.current_holdings_count[currency] = self.current_holdings_count.get(currency, 0) + 1

            logger.debug(f"🔄 [HOLDINGS-COUNT] Updated holdings count: {len(self.current_holdings_count)} currencies held")

        except Exception as e:
            logger.error(f"❌ [HOLDINGS-COUNT] Error updating holdings count: {e}")

    def _get_recent_strategy_usage(self) -> Dict[str, int]:
        """Get usage count of strategies in recent trades"""
        try:
            # Track strategy usage from recent trades (last 20 trades)
            recent_trades = getattr(self, 'recent_trade_history', [])[-20:]
            strategy_usage = {}

            for trade in recent_trades:
                strategy = trade.get('strategy', 'unknown')
                strategy_usage[strategy] = strategy_usage.get(strategy, 0) + 1

            return strategy_usage

        except Exception as e:
            logger.error(f"❌ [STRATEGY-USAGE] Error getting strategy usage: {e}")
            return {}

    def _find_underused_strategies(self, strategy_usage: Dict[str, int], available_strategies: Dict[str, List]) -> List[str]:
        """Find strategies that are underused and should be prioritized"""
        try:
            all_strategies = ['triangular_arbitrage', 'cross_exchange_arbitrage', 'portfolio_rebalancing', 'direct_currency_trading']
            total_recent_trades = sum(strategy_usage.values())

            if total_recent_trades == 0:
                return list(available_strategies.keys())

            underused = []
            target_usage_per_strategy = total_recent_trades / len(all_strategies)  # Ideal balanced usage

            for strategy in all_strategies:
                if strategy in available_strategies:
                    current_usage = strategy_usage.get(strategy, 0)
                    # Strategy is underused if it's used less than 70% of target usage
                    if current_usage < (target_usage_per_strategy * 0.7):
                        underused.append(strategy)

            logger.info(f"🎯 [STRATEGY-BALANCE] Underused strategies: {underused}")
            return underused

        except Exception as e:
            logger.error(f"❌ [STRATEGY-BALANCE] Error finding underused strategies: {e}")
            return []

    def _record_trade_for_strategy_balancing(self, opportunity, result: Dict):
        """Record trade information for strategy balancing and profit tracking"""
        try:
            import time

            # Calculate profit/loss
            profit_usd = result.get('profit_usd', 0)
            execution_time_ms = result.get('execution_time_ms', 0)

            trade_record = {
                'timestamp': time.time(),
                'strategy': opportunity.strategy,
                'pair': f"{opportunity.pair.base}{opportunity.pair.quote}",
                'side': opportunity.side,
                'success': result.get('success', False),
                'profit': result.get('profit', 0),
                'profit_usd': profit_usd,
                'confidence': opportunity.confidence,
                'execution_time_ms': execution_time_ms
            }

            self.recent_trade_history.append(trade_record)

            # Keep only last 50 trades
            if len(self.recent_trade_history) > 50:
                self.recent_trade_history = self.recent_trade_history[-50:]

            # Update profit tracking
            self._update_profit_tracking(trade_record)

            logger.debug(f"🎯 [TRADE-RECORD] Recorded {opportunity.strategy} trade for strategy balancing")

        except Exception as e:
            logger.error(f"❌ [TRADE-RECORD] Error recording trade: {e}")

    def _validate_profit_potential(self, opportunity) -> bool:
        """Validate that opportunity has minimum 0.5% profit potential after fees"""
        try:
            # Calculate expected fees (typical 0.1% maker + 0.1% taker = 0.2% total)
            estimated_fees_pct = 0.002  # 0.2%
            min_profit_after_fees = 0.005  # 0.5% minimum profit after fees

            # For direct trading, estimate profit from price movement potential
            if opportunity.strategy == 'direct_currency_trading':
                # ENHANCED: More strict profit validation for direct trading
                # Only high confidence trades with good profit potential should pass
                if opportunity.confidence < 0.75:  # Require high confidence
                    logger.debug(f"💰 [PROFIT-VALIDATION] {opportunity.pair.base}: Confidence {opportunity.confidence:.3f} too low for profit validation")
                    return False

                estimated_profit_pct = opportunity.confidence * 0.015  # Up to 1.5% profit for high confidence
                net_profit = estimated_profit_pct - estimated_fees_pct

                if net_profit >= min_profit_after_fees:
                    return True
                else:
                    logger.debug(f"💰 [PROFIT-VALIDATION] {opportunity.pair.base}: Net profit {net_profit:.3f} < minimum {min_profit_after_fees:.3f}")
                    return False

            # For arbitrage strategies, use expected profit directly
            elif opportunity.strategy in ['triangular_arbitrage', 'cross_exchange_arbitrage']:
                net_profit = float(opportunity.expected_profit) / 100 - estimated_fees_pct  # Convert percentage

                if net_profit >= min_profit_after_fees:
                    return True
                else:
                    logger.debug(f"💰 [PROFIT-VALIDATION] {opportunity.strategy}: Net profit {net_profit:.3f} < minimum {min_profit_after_fees:.3f}")
                    return False

            # For portfolio rebalancing, always allow (strategic value)
            elif opportunity.strategy == 'portfolio_rebalancing':
                return True

            # Default: require minimum confidence
            return opportunity.confidence >= 0.70

        except Exception as e:
            logger.error(f"❌ [PROFIT-VALIDATION] Error validating profit: {e}")
            return True  # Allow trade if validation fails

    def _validate_smart_trade_criteria(self, opportunity) -> bool:
        """Smart trade validation to prevent low-gain trades"""
        try:
            pair_symbol = f"{opportunity.pair.base}{opportunity.pair.quote}"
            current_time = time.time()

            # Check 1: Prevent opposite trades within 180 seconds
            recent_opposite_trades = [
                trade for trade in self.recent_trade_history[-10:]
                if (trade.get('pair') == pair_symbol and
                    trade.get('side') != opportunity.side and
                    current_time - trade.get('timestamp', 0) < 180)
            ]

            if recent_opposite_trades:
                logger.debug(f"🔄 [SMART-VALIDATION] {pair_symbol}: Opposite trade within 180s - blocking")
                return False

            # Check 2: Prevent trades with <0.2% net gain potential
            if opportunity.strategy == 'direct_currency_trading':
                estimated_gain = opportunity.confidence * 0.015  # Up to 1.5% gain
                if estimated_gain < 0.002:  # Less than 0.2%
                    logger.debug(f"🔄 [SMART-VALIDATION] {pair_symbol}: Estimated gain {estimated_gain:.3f} too low")
                    return False

            # Check 3: Ensure minimum trade value for efficiency
            trade_value = float(opportunity.amount) * float(opportunity.price)
            if trade_value < 5.0:  # Minimum $5 trade
                logger.debug(f"🔄 [SMART-VALIDATION] {pair_symbol}: Trade value ${trade_value:.2f} too small")
                return False

            # Check 4: Confidence threshold for smart trading
            if opportunity.confidence < 0.65:  # Higher threshold for smart validation
                logger.debug(f"🔄 [SMART-VALIDATION] {pair_symbol}: Confidence {opportunity.confidence:.3f} below smart threshold")
                return False

            return True

        except Exception as e:
            logger.error(f"❌ [SMART-VALIDATION] Error in smart validation: {e}")
            return True  # Allow trade if validation fails

    def _update_profit_tracking(self, trade_record: Dict):
        """Update profit tracking metrics for performance analysis"""
        try:
            import time

            # Update total metrics
            self.profit_tracker['total_trades'] += 1
            if trade_record['success']:
                self.profit_tracker['successful_trades'] += 1
                self.profit_tracker['total_profit_usd'] += trade_record.get('profit_usd', 0)

            # Update strategy performance
            strategy = trade_record['strategy']
            if strategy not in self.profit_tracker['strategy_performance']:
                self.profit_tracker['strategy_performance'][strategy] = {
                    'trades': 0,
                    'successful': 0,
                    'total_profit': 0,
                    'avg_execution_time': 0,
                    'success_rate': 0
                }

            strategy_stats = self.profit_tracker['strategy_performance'][strategy]
            strategy_stats['trades'] += 1
            if trade_record['success']:
                strategy_stats['successful'] += 1
                strategy_stats['total_profit'] += trade_record.get('profit_usd', 0)

            # Update execution time
            exec_time = trade_record.get('execution_time_ms', 0)
            if exec_time > 0:
                self.profit_tracker['execution_times'].append(exec_time)
                # Keep only last 100 execution times
                if len(self.profit_tracker['execution_times']) > 100:
                    self.profit_tracker['execution_times'] = self.profit_tracker['execution_times'][-100:]

                # Update strategy average execution time
                strategy_stats['avg_execution_time'] = (
                    strategy_stats['avg_execution_time'] * (strategy_stats['trades'] - 1) + exec_time
                ) / strategy_stats['trades']

            # Update success rate
            strategy_stats['success_rate'] = strategy_stats['successful'] / strategy_stats['trades']

            # Update pair performance
            pair = trade_record['pair']
            if pair not in self.profit_tracker['best_performing_pairs']:
                self.profit_tracker['best_performing_pairs'][pair] = {
                    'trades': 0,
                    'profit': 0,
                    'success_rate': 0
                }

            pair_stats = self.profit_tracker['best_performing_pairs'][pair]
            pair_stats['trades'] += 1
            if trade_record['success']:
                pair_stats['profit'] += trade_record.get('profit_usd', 0)
            pair_stats['success_rate'] = sum(1 for t in self.recent_trade_history[-20:]
                                           if t.get('pair') == pair and t.get('success', False)) / max(1, len([t for t in self.recent_trade_history[-20:] if t.get('pair') == pair]))

            # Log performance summary every 10 trades
            if self.profit_tracker['total_trades'] % 10 == 0:
                self._log_performance_summary()

        except Exception as e:
            logger.error(f"❌ [PROFIT-TRACKING] Error updating profit tracking: {e}")

    def _log_performance_summary(self):
        """Log performance summary for profit maximization analysis"""
        try:
            total_trades = self.profit_tracker['total_trades']
            successful_trades = self.profit_tracker['successful_trades']
            total_profit = self.profit_tracker['total_profit_usd']

            if total_trades == 0:
                return

            success_rate = (successful_trades / total_trades) * 100
            avg_profit_per_trade = total_profit / max(1, successful_trades)

            logger.info(f"💰 [PERFORMANCE] Total: {total_trades} trades, {success_rate:.1f}% success, ${total_profit:.2f} profit")
            logger.info(f"💰 [PERFORMANCE] Avg profit per successful trade: ${avg_profit_per_trade:.2f}")

            # Show strategy performance
            best_strategy = None
            best_profit = -999999
            for strategy, stats in self.profit_tracker['strategy_performance'].items():
                if stats['total_profit'] > best_profit:
                    best_profit = stats['total_profit']
                    best_strategy = strategy

                logger.info(f"📊 [STRATEGY] {strategy}: {stats['trades']} trades, {stats['success_rate']*100:.1f}% success, ${stats['total_profit']:.2f} profit")

            if best_strategy:
                logger.info(f"🏆 [BEST-STRATEGY] {best_strategy} with ${best_profit:.2f} profit")

            # Show execution performance
            if self.profit_tracker['execution_times']:
                avg_exec_time = sum(self.profit_tracker['execution_times']) / len(self.profit_tracker['execution_times'])
                logger.info(f"⚡ [EXECUTION] Average execution time: {avg_exec_time:.1f}ms")

        except Exception as e:
            logger.error(f"❌ [PERFORMANCE-LOG] Error logging performance: {e}")

    async def _gather_market_intelligence(self) -> Dict[str, Any]:
        """Gather comprehensive market intelligence from all available data sources"""
        try:
            intelligence = {
                'sentiment_data': {},
                'volume_data': {},
                'price_trends': {},
                'liquidity_metrics': {},
                'web_crawler_insights': {},
                'dex_screener_data': {},
                'timestamp': time.time()
            }

            # Try to gather sentiment analysis data
            try:
                if hasattr(self, 'sentiment_analyzer'):
                    sentiment_data = await self.sentiment_analyzer.get_market_sentiment()
                    intelligence['sentiment_data'] = sentiment_data
                    logger.debug("📊 [INTELLIGENCE] Gathered sentiment analysis data")
            except Exception as e:
                logger.debug(f"⚠️ [INTELLIGENCE] Sentiment data unavailable: {e}")

            # Try to gather web crawler data
            try:
                # Check if internet crawler is available from main system
                if hasattr(self, 'internet_crawler') or 'fast_trading_data' in dir():
                    from src.data_feeds.internet_crawler import get_fast_trading_data
                    crawler_data = get_fast_trading_data()
                    intelligence['web_crawler_insights'] = crawler_data
                    logger.debug("📊 [INTELLIGENCE] Gathered web crawler insights")
            except Exception as e:
                logger.debug(f"⚠️ [INTELLIGENCE] Web crawler data unavailable: {e}")

            # Gather real-time price and volume data from exchanges
            try:
                for exchange_name, client in self.exchange_clients.items():
                    if hasattr(client, 'get_24hr_ticker'):
                        ticker_data = await client.get_24hr_ticker()
                        if ticker_data:
                            intelligence['volume_data'][exchange_name] = ticker_data
                            logger.debug(f"📊 [INTELLIGENCE] Gathered {exchange_name} volume data")
            except Exception as e:
                logger.debug(f"⚠️ [INTELLIGENCE] Exchange data gathering error: {e}")

            # Calculate price trends from recent data
            try:
                intelligence['price_trends'] = await self._calculate_price_trends()
                logger.debug("📊 [INTELLIGENCE] Calculated price trends")
            except Exception as e:
                logger.debug(f"⚠️ [INTELLIGENCE] Price trend calculation error: {e}")

            logger.info(f"📊 [INTELLIGENCE] Market intelligence gathered from {len([k for k, v in intelligence.items() if v and k != 'timestamp'])} sources")
            return intelligence

        except Exception as e:
            logger.error(f"❌ [INTELLIGENCE] Error gathering market intelligence: {e}")
            return {'timestamp': time.time()}

    async def _calculate_price_trends(self) -> Dict[str, float]:
        """Calculate price trends for major currencies"""
        try:
            trends = {}

            # Get recent price data for trend analysis
            for exchange_name, client in self.exchange_clients.items():
                try:
                    # Get recent kline data for trend calculation
                    major_pairs = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT']

                    for symbol in major_pairs:
                        try:
                            if hasattr(client, 'get_kline_data'):
                                # Fix: Use correct Bybit interval format
                                klines = await client.get_kline_data(symbol, '1', 20)  # Last 20 minutes (1 = 1 minute)
                                if klines and len(klines) >= 2:
                                    # Calculate simple trend (price change over period)
                                    first_price = float(klines[0]['close'])
                                    last_price = float(klines[-1]['close'])
                                    trend = (last_price - first_price) / first_price
                                    trends[symbol] = trend
                        except Exception:
                            continue

                except Exception:
                    continue

            return trends

        except Exception as e:
            logger.error(f"❌ [PRICE-TRENDS] Error calculating price trends: {e}")
            return {}

    async def _find_direct_trading_opportunities_enhanced(self, market_intelligence: Dict[str, Any]) -> List[TradingOpportunity]:
        """Enhanced direct trading opportunities using market intelligence"""
        try:
            # Get basic opportunities first
            basic_opportunities = await self._find_direct_trading_opportunities()

            # Enhance opportunities with market intelligence
            enhanced_opportunities = []

            for opp in basic_opportunities:
                enhanced_opp = self._enhance_opportunity_with_intelligence(opp, market_intelligence)
                enhanced_opportunities.append(enhanced_opp)

            # Sort by enhanced confidence and profit potential
            enhanced_opportunities.sort(key=lambda x: (x.confidence, x.expected_profit), reverse=True)

            logger.info(f"📊 [ENHANCED-OPPORTUNITIES] Enhanced {len(enhanced_opportunities)} opportunities with market intelligence")
            return enhanced_opportunities

        except Exception as e:
            logger.error(f"❌ [ENHANCED-OPPORTUNITIES] Error enhancing opportunities: {e}")
            # Fallback to basic opportunities
            return await self._find_direct_trading_opportunities()

    def _enhance_opportunity_with_intelligence(self, opportunity, intelligence: Dict[str, Any]):
        """Enhance a trading opportunity with market intelligence"""
        try:
            pair_symbol = f"{opportunity.pair.base}{opportunity.pair.quote}"

            # Base confidence
            enhanced_confidence = opportunity.confidence

            # Enhance with sentiment data
            sentiment_data = intelligence.get('sentiment_data', {})
            if pair_symbol in sentiment_data:
                sentiment_score = sentiment_data[pair_symbol].get('score', 0)
                if sentiment_score > 0.6:  # Positive sentiment
                    enhanced_confidence += 0.1
                elif sentiment_score < 0.4:  # Negative sentiment
                    enhanced_confidence -= 0.1

            # Enhance with price trends
            price_trends = intelligence.get('price_trends', {})
            if pair_symbol in price_trends:
                trend = price_trends[pair_symbol]
                if opportunity.side == 'buy' and trend > 0.01:  # Upward trend for buy
                    enhanced_confidence += 0.15
                elif opportunity.side == 'sell' and trend < -0.01:  # Downward trend for sell
                    enhanced_confidence += 0.15
                elif (opportunity.side == 'buy' and trend < -0.02) or (opportunity.side == 'sell' and trend > 0.02):
                    enhanced_confidence -= 0.1  # Against trend

            # Enhance with volume data
            volume_data = intelligence.get('volume_data', {})
            for exchange_data in volume_data.values():
                if pair_symbol in exchange_data:
                    volume_change = exchange_data[pair_symbol].get('volume_change_24h', 0)
                    if volume_change > 0.2:  # High volume increase
                        enhanced_confidence += 0.05

            # Ensure confidence stays within bounds
            enhanced_confidence = max(0.1, min(1.0, enhanced_confidence))

            # Create enhanced opportunity
            opportunity.confidence = enhanced_confidence

            return opportunity

        except Exception as e:
            logger.error(f"❌ [ENHANCE-OPPORTUNITY] Error enhancing opportunity: {e}")
            return opportunity

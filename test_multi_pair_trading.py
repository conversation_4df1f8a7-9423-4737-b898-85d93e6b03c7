#!/usr/bin/env python3
"""
Comprehensive Multi-Pair Trading Validation Test
Tests the enhanced trading system to ensure proper diversification and strategy rotation
"""

import asyncio
import os
import sys
import time
from pathlib import Path
from datetime import datetime
from decimal import Decimal

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# Set environment for testing
os.environ.update({
    'LIVE_TRADING': 'true',
    'REAL_MONEY_TRADING': 'true',
    'BYBIT_ONLY_MODE': 'true',
    'COINBASE_ENABLED': 'false'
})

async def test_multi_pair_diversification():
    """Test that the system properly diversifies across multiple trading pairs"""
    print("🧪 [TEST] Testing Multi-Pair Trading Diversification")
    print("=" * 60)
    
    try:
        # Import the enhanced multi-currency trading engine
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Get API credentials
        bybit_api_key = os.getenv('BYBIT_API_KEY')
        bybit_api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not bybit_api_key or not bybit_api_secret:
            print("❌ [TEST] Missing Bybit API credentials")
            return False
        
        # Initialize exchange client
        bybit_client = BybitClientFixed(
            api_key=bybit_api_key,
            api_secret=bybit_api_secret,
            testnet=False
        )
        
        exchange_clients = {'bybit': bybit_client}
        
        # Initialize trading engine with test configuration
        trading_engine = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={
                'aggressive_trading': True,
                'confidence_threshold': 0.60,
                'max_balance_usage': 0.85,
                'enable_dynamic_discovery': True,
                'auto_discover_pairs': True,
                'pair_cooldown_duration': 60,  # Shorter cooldown for testing
                'strategy_cooldown_duration': 30,
                'max_consecutive_same_pair': 1  # Force diversification
            }
        )
        
        print("✅ [TEST] Trading engine initialized")
        
        # Initialize the engine
        await trading_engine.initialize()
        print("✅ [TEST] Trading engine components initialized")
        
        # Test 1: Verify currency discovery
        print("\n🔍 [TEST-1] Testing Currency Discovery")
        discovered_currencies = len(trading_engine.supported_currencies)
        active_pairs = sum(len(pairs) for pairs in trading_engine.active_pairs.values())
        
        print(f"📊 [TEST-1] Discovered currencies: {discovered_currencies}")
        print(f"📊 [TEST-1] Active trading pairs: {active_pairs}")
        
        if discovered_currencies < 5:
            print("⚠️ [TEST-1] WARNING: Low currency discovery count")
        else:
            print("✅ [TEST-1] PASS: Good currency discovery")
        
        # Test 2: Verify diversified target currency selection
        print("\n🎯 [TEST-2] Testing Diversified Target Currency Selection")
        
        # Mock current balances for testing
        mock_balances = {
            'USDT': 100.0,
            'BTC': 0.001,
            'ETH': 0.05
        }
        
        target_currencies = await trading_engine._get_diversified_target_currencies('bybit', mock_balances)
        
        print(f"📊 [TEST-2] Target currencies: {target_currencies[:10]}")
        print(f"📊 [TEST-2] Total targets: {len(target_currencies)}")
        
        # Verify diversification
        has_non_btc = any(curr != 'BTC' for curr in target_currencies[:5])
        if has_non_btc and len(target_currencies) >= 5:
            print("✅ [TEST-2] PASS: Good diversification in target selection")
        else:
            print("❌ [TEST-2] FAIL: Poor diversification in target selection")
        
        # Test 3: Test opportunity generation with diversification
        print("\n🔍 [TEST-3] Testing Opportunity Generation with Diversification")
        
        opportunities = await trading_engine.find_trading_opportunities()
        
        print(f"📊 [TEST-3] Total opportunities found: {len(opportunities)}")
        
        # Analyze opportunity diversity
        unique_pairs = set()
        unique_strategies = set()
        
        for opp in opportunities:
            pair_symbol = f"{opp.pair.base}{opp.pair.quote}"
            unique_pairs.add(pair_symbol)
            unique_strategies.add(opp.strategy)
        
        print(f"📊 [TEST-3] Unique trading pairs: {len(unique_pairs)}")
        print(f"📊 [TEST-3] Unique strategies: {len(unique_strategies)}")
        print(f"📊 [TEST-3] Sample pairs: {list(unique_pairs)[:5]}")
        print(f"📊 [TEST-3] Sample strategies: {list(unique_strategies)}")
        
        # Verify diversification
        if len(unique_pairs) >= 3 and len(unique_strategies) >= 2:
            print("✅ [TEST-3] PASS: Good opportunity diversification")
        else:
            print("❌ [TEST-3] FAIL: Poor opportunity diversification")
        
        # Test 4: Test cooldown mechanism
        print("\n⏰ [TEST-4] Testing Cooldown Mechanism")
        
        # Add some pairs to cooldown
        trading_engine._add_pair_cooldown('BTCUSDT')
        trading_engine._add_strategy_cooldown('direct_currency_trading')
        
        # Check cooldown status
        btc_on_cooldown = trading_engine._is_pair_on_cooldown('BTCUSDT')
        strategy_on_cooldown = trading_engine._is_strategy_on_cooldown('direct_currency_trading')
        
        print(f"📊 [TEST-4] BTCUSDT on cooldown: {btc_on_cooldown}")
        print(f"📊 [TEST-4] Direct trading on cooldown: {strategy_on_cooldown}")
        
        if btc_on_cooldown and strategy_on_cooldown:
            print("✅ [TEST-4] PASS: Cooldown mechanism working")
        else:
            print("❌ [TEST-4] FAIL: Cooldown mechanism not working")
        
        # Test 5: Test strategy balancing
        print("\n⚖️ [TEST-5] Testing Strategy Balancing")
        
        # Mock some trade history
        trading_engine.recent_trade_history = [
            {'strategy': 'direct_currency_trading', 'timestamp': time.time() - 100},
            {'strategy': 'direct_currency_trading', 'timestamp': time.time() - 200},
            {'strategy': 'direct_currency_trading', 'timestamp': time.time() - 300},
        ]
        
        strategy_usage = trading_engine._get_recent_strategy_usage()
        underused_strategies = trading_engine._find_underused_strategies(strategy_usage, {
            'triangular_arbitrage': [],
            'cross_exchange_arbitrage': [],
            'portfolio_rebalancing': [],
            'direct_currency_trading': []
        })
        
        print(f"📊 [TEST-5] Strategy usage: {strategy_usage}")
        print(f"📊 [TEST-5] Underused strategies: {underused_strategies}")
        
        if len(underused_strategies) >= 2:
            print("✅ [TEST-5] PASS: Strategy balancing working")
        else:
            print("❌ [TEST-5] FAIL: Strategy balancing not working properly")
        
        # Test 6: Test profit validation
        print("\n💰 [TEST-6] Testing Profit Validation")
        
        # Create mock opportunity
        from src.trading.multi_currency_trading_engine import TradingOpportunity, CurrencyPair
        
        mock_pair = CurrencyPair(
            base='ETH',
            quote='USDT',
            symbol='ETHUSDT',
            exchange='bybit',
            min_order_value=Decimal('5.0'),
            tick_size=Decimal('0.01'),
            lot_size=Decimal('0.001'),
            is_active=True,
            liquidity_score=1.0
        )
        
        high_confidence_opp = TradingOpportunity(
            pair=mock_pair,
            side='buy',
            amount=Decimal('0.1'),
            price=Decimal('2000.0'),
            expected_profit=Decimal('1.0'),
            confidence=0.85,
            strategy='direct_currency_trading',
            risk_score=0.3,
            execution_priority=1
        )
        
        low_confidence_opp = TradingOpportunity(
            pair=mock_pair,
            side='buy',
            amount=Decimal('0.1'),
            price=Decimal('2000.0'),
            expected_profit=Decimal('0.1'),
            confidence=0.45,
            strategy='direct_currency_trading',
            risk_score=0.3,
            execution_priority=3
        )
        
        high_profit_valid = trading_engine._validate_profit_potential(high_confidence_opp)
        low_profit_valid = trading_engine._validate_profit_potential(low_confidence_opp)
        
        print(f"📊 [TEST-6] High confidence opportunity valid: {high_profit_valid}")
        print(f"📊 [TEST-6] Low confidence opportunity valid: {low_profit_valid}")
        
        if high_profit_valid and not low_profit_valid:
            print("✅ [TEST-6] PASS: Profit validation working correctly")
        else:
            print("❌ [TEST-6] FAIL: Profit validation not working properly")
        
        # Test Summary
        print("\n📊 [TEST-SUMMARY] Multi-Pair Trading Test Results")
        print("=" * 60)
        
        tests_passed = 0
        total_tests = 6
        
        if discovered_currencies >= 5:
            tests_passed += 1
        if has_non_btc and len(target_currencies) >= 5:
            tests_passed += 1
        if len(unique_pairs) >= 3 and len(unique_strategies) >= 2:
            tests_passed += 1
        if btc_on_cooldown and strategy_on_cooldown:
            tests_passed += 1
        if len(underused_strategies) >= 2:
            tests_passed += 1
        if high_profit_valid and not low_profit_valid:
            tests_passed += 1
        
        success_rate = (tests_passed / total_tests) * 100
        
        print(f"🎯 [SUMMARY] Tests passed: {tests_passed}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("🎉 [SUMMARY] OVERALL RESULT: PASS - Multi-pair trading system working well")
            return True
        else:
            print("⚠️ [SUMMARY] OVERALL RESULT: NEEDS IMPROVEMENT - Some issues detected")
            return False
        
    except Exception as e:
        print(f"❌ [TEST] Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test execution"""
    print("🚀 Multi-Pair Trading Validation Test")
    print("Testing enhanced trading system diversification and strategy rotation")
    print("=" * 80)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run the test
    success = await test_multi_pair_diversification()
    
    if success:
        print("\n✅ All tests completed successfully!")
        print("🎯 The trading system is properly configured for multi-pair diversification")
        return 0
    else:
        print("\n❌ Some tests failed!")
        print("⚠️ The trading system needs improvements for proper diversification")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

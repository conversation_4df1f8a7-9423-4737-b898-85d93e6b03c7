#!/usr/bin/env python3
"""
Test Order Execution and Pair Diversification Fixes

This script tests:
1. Order execution pipeline with proper status monitoring
2. Trading pair diversification and rotation
3. Verification that orders complete successfully instead of getting stuck in 'submitted' status
4. Verification that the system rotates between different cryptocurrency pairs
"""

import asyncio
import logging
import os
import sys
import time
from decimal import Decimal
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s'
)
logger = logging.getLogger("OrderExecutionTest")

async def test_order_execution_monitoring():
    """Test the enhanced order execution with status monitoring"""
    logger.info("🧪 [TEST-1] Testing Order Execution Monitoring...")
    
    try:
        # Import the enhanced Bybit client
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Initialize client
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not api_key or not api_secret:
            logger.error("❌ [TEST-1] Bybit credentials not found")
            return False
        
        client = BybitClientFixed(api_key, api_secret)
        
        # Test 1: Check balance
        logger.info("🔧 [TEST-1.1] Checking USDT balance...")
        balance = await client.get_balance('USDT')
        logger.info(f"💰 [TEST-1.1] USDT Balance: ${balance:.2f}")
        
        if balance < 5.0:
            logger.warning("⚠️ [TEST-1.1] Insufficient balance for order test")
            return False
        
        # Test 2: Place a small test order and monitor status
        logger.info("🔧 [TEST-1.2] Placing test order with status monitoring...")
        
        test_amount = min(5.0, float(balance) * 0.1)  # Use 10% of balance or $5, whichever is smaller
        
        result = await client.place_order(
            symbol="BTCUSDT",
            side="buy",
            amount=test_amount,
            order_type="market",
            is_quote_amount=True
        )
        
        logger.info(f"📊 [TEST-1.2] Order Result: {result}")
        
        # Check if order has proper status (not just 'submitted')
        order_status = result.get('status', 'unknown')
        order_id = result.get('order_id', 'N/A')
        filled_qty = result.get('filled_qty', '0')
        
        logger.info(f"📊 [TEST-1.2] Order ID: {order_id}")
        logger.info(f"📊 [TEST-1.2] Status: {order_status}")
        logger.info(f"📊 [TEST-1.2] Filled Qty: {filled_qty}")
        
        # Verify order completed successfully
        if order_status in ['filled', 'partially_filled']:
            logger.info("✅ [TEST-1.2] Order execution monitoring WORKING - order completed successfully")
            return True
        elif order_status == 'timeout':
            logger.warning("⚠️ [TEST-1.2] Order monitoring timeout - may need adjustment")
            return False
        elif order_status == 'failed':
            logger.error("❌ [TEST-1.2] Order execution failed")
            return False
        else:
            logger.warning(f"⚠️ [TEST-1.2] Unexpected order status: {order_status}")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-1] Order execution test failed: {e}")
        return False

async def test_pair_diversification():
    """Test the enhanced pair diversification system"""
    logger.info("🧪 [TEST-2] Testing Pair Diversification...")
    
    try:
        # Import the multi-currency trading engine
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Initialize exchange client
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not api_key or not api_secret:
            logger.error("❌ [TEST-2] Bybit credentials not found")
            return False
        
        bybit_client = BybitClientFixed(api_key, api_secret)
        exchange_clients = {'bybit': bybit_client}
        
        # Initialize trading engine with aggressive diversification settings
        trading_engine = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={
                'aggressive_trading': True,
                'confidence_threshold': 0.60,
                'max_balance_usage': 0.85,
                'enable_dynamic_discovery': True,
                'auto_discover_pairs': True,
                'pair_cooldown_duration': 60,  # 1 minute for testing
                'strategy_cooldown_duration': 30,  # 30 seconds for testing
                'max_consecutive_same_pair': 1,  # Force immediate diversification
                'diversification_enforcement': True
            }
        )
        
        # Test 2.1: Initialize the engine
        logger.info("🔧 [TEST-2.1] Initializing trading engine...")
        await trading_engine.initialize()
        
        # Test 2.2: Check diversified target currencies
        logger.info("🔧 [TEST-2.2] Getting diversified target currencies...")
        
        # Get current balances
        balances = await bybit_client.get_all_available_balances()
        logger.info(f"💰 [TEST-2.2] Current balances: {balances}")
        
        # Get diversified currencies
        target_currencies = await trading_engine._get_diversified_target_currencies('bybit', balances)
        logger.info(f"🎯 [TEST-2.2] Diversified targets: {target_currencies}")
        
        if len(target_currencies) > 1:
            logger.info("✅ [TEST-2.2] Diversification system generating multiple target currencies")
        else:
            logger.warning("⚠️ [TEST-2.2] Limited diversification targets")
        
        # Test 2.3: Find trading opportunities with diversification
        logger.info("🔧 [TEST-2.3] Finding diversified trading opportunities...")
        
        opportunities = await trading_engine.find_trading_opportunities()
        logger.info(f"🎯 [TEST-2.3] Found {len(opportunities)} opportunities")
        
        # Check for pair diversity
        pairs_found = set()
        strategies_found = set()
        
        for opp in opportunities[:10]:  # Check first 10
            pair_symbol = f"{opp.pair.base}{opp.pair.quote}"
            pairs_found.add(pair_symbol)
            strategies_found.add(opp.strategy)
            logger.info(f"🎯 [TEST-2.3] Opportunity: {opp.strategy} - {pair_symbol} - {opp.side} - Confidence: {opp.confidence:.3f}")
        
        logger.info(f"🎯 [TEST-2.3] Unique pairs found: {len(pairs_found)}")
        logger.info(f"🎯 [TEST-2.3] Unique strategies found: {len(strategies_found)}")
        logger.info(f"🎯 [TEST-2.3] Pairs: {list(pairs_found)}")
        
        if len(pairs_found) > 1:
            logger.info("✅ [TEST-2.3] Pair diversification WORKING - multiple pairs found")
            return True
        else:
            logger.warning("⚠️ [TEST-2.3] Limited pair diversification - may need adjustment")
            return False
            
    except Exception as e:
        logger.error(f"❌ [TEST-2] Pair diversification test failed: {e}")
        return False

async def test_cooldown_system():
    """Test the enhanced cooldown system"""
    logger.info("🧪 [TEST-3] Testing Enhanced Cooldown System...")
    
    try:
        from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
        from src.exchanges.bybit_client_fixed import BybitClientFixed
        
        # Initialize minimal engine for cooldown testing
        bybit_client = BybitClientFixed(os.getenv('BYBIT_API_KEY'), os.getenv('BYBIT_API_SECRET'))
        exchange_clients = {'bybit': bybit_client}
        
        trading_engine = MultiCurrencyTradingEngine(
            exchange_clients=exchange_clients,
            config={'max_consecutive_same_pair': 1}
        )
        
        # Test cooldown functionality
        test_pair = "BTCUSDT"
        
        # Test 3.1: Add pair to cooldown
        logger.info("🔧 [TEST-3.1] Testing pair cooldown addition...")
        trading_engine._add_pair_cooldown(test_pair)
        
        # Test 3.2: Check if pair is on cooldown
        logger.info("🔧 [TEST-3.2] Testing cooldown detection...")
        is_on_cooldown = trading_engine._is_pair_on_cooldown(test_pair)
        
        if is_on_cooldown:
            logger.info("✅ [TEST-3.2] Cooldown system WORKING - pair correctly on cooldown")
        else:
            logger.error("❌ [TEST-3.2] Cooldown system FAILED - pair not on cooldown")
            return False
        
        # Test 3.3: Test diversification enforcement
        logger.info("🔧 [TEST-3.3] Testing diversification enforcement...")
        should_force = trading_engine._should_force_diversification(test_pair)
        
        if should_force:
            logger.info("✅ [TEST-3.3] Diversification enforcement WORKING")
        else:
            logger.warning("⚠️ [TEST-3.3] Diversification enforcement may need adjustment")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST-3] Cooldown system test failed: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 [MAIN] Starting Order Execution and Diversification Tests...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    test_results = {}
    
    # Test 1: Order Execution Monitoring
    test_results['order_execution'] = await test_order_execution_monitoring()
    
    # Test 2: Pair Diversification
    test_results['pair_diversification'] = await test_pair_diversification()
    
    # Test 3: Cooldown System
    test_results['cooldown_system'] = await test_cooldown_system()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 [SUMMARY] Test Results:")
    logger.info("="*60)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    total_passed = sum(test_results.values())
    total_tests = len(test_results)
    
    logger.info(f"\n📊 [SUMMARY] {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        logger.info("🎉 [SUCCESS] All tests passed - fixes are working!")
        return True
    else:
        logger.error("❌ [FAILURE] Some tests failed - fixes need adjustment")
        return False

if __name__ == "__main__":
    asyncio.run(main())

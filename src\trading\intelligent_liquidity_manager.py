"""
Intelligent Liquidity Management System

Advanced liquidity management system that ensures continuous trading capability
by maintaining minimum balances across multiple currencies, with automatic
liquidity provision and withdrawal strategies based on institutional
liquidity management practices.
"""

import asyncio
import logging
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import time
import math

logger = logging.getLogger(__name__)

class LiquidityAction(Enum):
    """Types of liquidity management actions"""
    PROVISION = "provision"         # Add liquidity to exchange
    WITHDRAWAL = "withdrawal"       # Remove liquidity from exchange
    REBALANCE = "rebalance"        # Rebalance between exchanges
    EMERGENCY_PROVISION = "emergency_provision"  # Emergency liquidity injection

class LiquidityStatus(Enum):
    """Liquidity status levels"""
    ABUNDANT = "abundant"           # >150% of minimum required
    ADEQUATE = "adequate"           # 100-150% of minimum required
    LOW = "low"                     # 50-100% of minimum required
    CRITICAL = "critical"           # <50% of minimum required
    DEPLETED = "depleted"          # <10% of minimum required

@dataclass
class LiquidityRequirement:
    """Liquidity requirement for a currency on an exchange"""
    currency: str
    exchange: str
    minimum_balance: Decimal
    target_balance: Decimal
    current_balance: Decimal
    status: LiquidityStatus
    priority: int
    last_update: float

@dataclass
class LiquidityActionItem:
    """Action to manage liquidity"""
    action_id: str
    action_type: LiquidityAction  # Reference to the enum
    currency: str
    source_exchange: Optional[str]
    target_exchange: str
    amount: Decimal
    priority: int
    reason: str
    estimated_cost: Decimal
    estimated_time: float
    dependencies: List[str]

class IntelligentLiquidityManager:
    """
    Advanced liquidity management system implementing institutional-grade
    liquidity management strategies with automatic provision and withdrawal
    """
    
    def __init__(self, exchange_clients: Dict[str, Any], config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}
        
        # Liquidity configuration
        self.minimum_balances = self.config.get('minimum_balances', {})
        self.target_multiplier = self.config.get('target_multiplier', 2.0)  # 2x minimum as target
        self.emergency_threshold = self.config.get('emergency_threshold', 0.1)  # 10% of minimum
        self.rebalance_threshold = self.config.get('rebalance_threshold', 0.3)  # 30% imbalance
        
        # Default minimum balances (USD equivalent)
        self.default_minimums = {
            'USDT': Decimal('50'),    # $50 minimum USDT
            'USD': Decimal('50'),     # $50 minimum USD
            'BTC': Decimal('20'),     # $20 minimum BTC
            'ETH': Decimal('20'),     # $20 minimum ETH
            'SOL': Decimal('15'),     # $15 minimum SOL
            'ADA': Decimal('10'),     # $10 minimum ADA
            'DOT': Decimal('10'),     # $10 minimum DOT
            'LINK': Decimal('10'),    # $10 minimum LINK
            'UNI': Decimal('10'),     # $10 minimum UNI
            'AVAX': Decimal('10'),    # $10 minimum AVAX
        }
        
        # Liquidity state tracking
        self.liquidity_requirements = {}
        self.liquidity_history = []
        self.pending_actions = {}
        
        # Performance metrics
        self.liquidity_stats = {
            'total_provisions': 0,
            'total_withdrawals': 0,
            'emergency_provisions': 0,
            'average_response_time': 0.0
        }
        
        logger.info("💧 [LIQUIDITY] Initialized intelligent liquidity manager")
    
    async def initialize(self):
        """Initialize the liquidity manager"""
        try:
            logger.info("🔧 [LIQUIDITY] Initializing liquidity manager...")
            
            # Assess current liquidity across all exchanges
            await self.assess_current_liquidity()
            
            # Calculate liquidity requirements
            await self.calculate_liquidity_requirements()
            
            # Identify immediate liquidity needs
            await self.identify_liquidity_needs()
            
            logger.info("✅ [LIQUIDITY] Liquidity manager initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error initializing liquidity manager: {e}")
            raise
    
    async def assess_current_liquidity(self):
        """Assess current liquidity across all exchanges"""
        try:
            logger.info("📊 [LIQUIDITY] Assessing current liquidity...")
            
            total_liquidity = {}
            exchange_liquidity = {}
            
            for exchange_name, client in self.exchange_clients.items():
                try:
                    if hasattr(client, 'get_all_available_balances'):
                        balances = await client.get_all_available_balances()
                        exchange_liquidity[exchange_name] = balances
                        
                        # Add to total liquidity
                        for currency, balance in balances.items():
                            if balance > 0:
                                total_liquidity[currency] = total_liquidity.get(currency, 0) + balance
                        
                        logger.info(f"📊 [LIQUIDITY] {exchange_name}: {len(balances)} currencies")
                
                except Exception as e:
                    logger.warning(f"⚠️ [LIQUIDITY] Error getting balances from {exchange_name}: {e}")
                    continue
            
            # Log total liquidity summary
            logger.info(f"📊 [LIQUIDITY] Total liquidity across {len(exchange_liquidity)} exchanges:")
            for currency, total_balance in total_liquidity.items():
                if total_balance > 0:
                    usd_value = await self._get_usd_value(currency, total_balance)
                    logger.info(f"📊 [LIQUIDITY] {currency}: {total_balance:.6f} (${usd_value:.2f})")
            
            self.current_liquidity = {
                'total': total_liquidity,
                'by_exchange': exchange_liquidity,
                'last_update': time.time()
            }
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error assessing liquidity: {e}")
    
    async def calculate_liquidity_requirements(self):
        """Calculate liquidity requirements for each currency and exchange"""
        try:
            logger.info("🎯 [LIQUIDITY] Calculating liquidity requirements...")
            
            self.liquidity_requirements = {}
            
            for exchange_name in self.exchange_clients.keys():
                exchange_requirements = {}
                
                # Get current balances for this exchange
                current_balances = self.current_liquidity['by_exchange'].get(exchange_name, {})
                
                # Calculate requirements for each currency
                for currency in self.default_minimums.keys():
                    # Get minimum balance requirement
                    min_balance_usd = self.minimum_balances.get(currency, self.default_minimums[currency])
                    min_balance = await self._convert_from_usd(currency, min_balance_usd)
                    
                    # Calculate target balance
                    target_balance = min_balance * Decimal(str(self.target_multiplier))
                    
                    # Get current balance
                    current_balance = Decimal(str(current_balances.get(currency, 0)))
                    
                    # Determine status
                    status = self._determine_liquidity_status(current_balance, min_balance)
                    
                    # Calculate priority (higher number = higher priority)
                    priority = self._calculate_liquidity_priority(status, currency)
                    
                    requirement = LiquidityRequirement(
                        currency=currency,
                        exchange=exchange_name,
                        minimum_balance=min_balance,
                        target_balance=target_balance,
                        current_balance=current_balance,
                        status=status,
                        priority=priority,
                        last_update=time.time()
                    )
                    
                    exchange_requirements[currency] = requirement
                    
                    # Log requirement if not adequate
                    if status != LiquidityStatus.ADEQUATE and status != LiquidityStatus.ABUNDANT:
                        logger.info(f"🎯 [LIQUIDITY] {exchange_name} {currency}: {status.value}")
                        logger.info(f"  Current: {current_balance:.6f}, Min: {min_balance:.6f}, Target: {target_balance:.6f}")
                
                self.liquidity_requirements[exchange_name] = exchange_requirements
            
            logger.info(f"🎯 [LIQUIDITY] Calculated requirements for {len(self.liquidity_requirements)} exchanges")
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating requirements: {e}")
    
    def _determine_liquidity_status(self, current: Decimal, minimum: Decimal) -> LiquidityStatus:
        """Determine liquidity status based on current vs minimum balance"""
        if minimum <= 0:
            return LiquidityStatus.ABUNDANT
        
        ratio = current / minimum
        
        if ratio >= 1.5:
            return LiquidityStatus.ABUNDANT
        elif ratio >= 1.0:
            return LiquidityStatus.ADEQUATE
        elif ratio >= 0.5:
            return LiquidityStatus.LOW
        elif ratio >= 0.1:
            return LiquidityStatus.CRITICAL
        else:
            return LiquidityStatus.DEPLETED
    
    def _calculate_liquidity_priority(self, status: LiquidityStatus, currency: str) -> int:
        """Calculate priority for liquidity management"""
        base_priority = {
            LiquidityStatus.DEPLETED: 100,
            LiquidityStatus.CRITICAL: 80,
            LiquidityStatus.LOW: 60,
            LiquidityStatus.ADEQUATE: 20,
            LiquidityStatus.ABUNDANT: 10
        }.get(status, 50)
        
        # Adjust priority based on currency importance
        currency_multiplier = {
            'USDT': 1.5,  # USDT is most important for trading
            'USD': 1.5,   # USD is also critical
            'BTC': 1.3,   # Major cryptocurrencies
            'ETH': 1.3,
            'SOL': 1.1,
            'ADA': 1.0,
            'DOT': 1.0,
        }.get(currency, 1.0)
        
        return int(base_priority * currency_multiplier)
    
    async def identify_liquidity_needs(self) -> List[LiquidityActionItem]:
        """Identify immediate liquidity management needs"""
        try:
            logger.info("🔍 [LIQUIDITY] Identifying liquidity needs...")
            
            liquidity_actions = []
            
            for exchange_name, requirements in self.liquidity_requirements.items():
                for currency, requirement in requirements.items():
                    
                    # Skip if liquidity is adequate or abundant
                    if requirement.status in [LiquidityStatus.ADEQUATE, LiquidityStatus.ABUNDANT]:
                        continue
                    
                    # Calculate needed amount
                    needed_amount = requirement.target_balance - requirement.current_balance
                    
                    if needed_amount > 0:
                        # Determine action type based on status
                        if requirement.status == LiquidityStatus.DEPLETED:
                            action_type = LiquidityAction.EMERGENCY_PROVISION
                        else:
                            action_type = LiquidityAction.PROVISION
                        
                        # Find source of liquidity
                        source_exchange = await self._find_liquidity_source(currency, needed_amount, exchange_name)
                        
                        action = LiquidityActionItem(
                            action_id=f"liq_{int(time.time())}_{currency}_{exchange_name}",
                            action_type=action_type,
                            currency=currency,
                            source_exchange=source_exchange,
                            target_exchange=exchange_name,
                            amount=needed_amount,
                            priority=requirement.priority,
                            reason=f"Liquidity {requirement.status.value} - need {needed_amount:.6f} {currency}",
                            estimated_cost=await self._estimate_transfer_cost(currency, needed_amount),
                            estimated_time=await self._estimate_transfer_time(source_exchange, exchange_name),
                            dependencies=[]
                        )
                        
                        liquidity_actions.append(action)
                        
                        logger.info(f"🔍 [LIQUIDITY] Action needed: {action.reason}")
                        logger.info(f"  {action.source_exchange} -> {action.target_exchange}: {action.amount:.6f} {action.currency}")
            
            # Sort by priority (highest first)
            liquidity_actions.sort(key=lambda x: x.priority, reverse=True)
            
            logger.info(f"🔍 [LIQUIDITY] Identified {len(liquidity_actions)} liquidity actions")
            
            return liquidity_actions
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error identifying needs: {e}")
            return []
    
    async def _find_liquidity_source(self, currency: str, needed_amount: Decimal, 
                                   target_exchange: str) -> Optional[str]:
        """Find the best source of liquidity for a currency"""
        try:
            best_source = None
            best_available = Decimal('0')
            
            for exchange_name, balances in self.current_liquidity['by_exchange'].items():
                if exchange_name == target_exchange:
                    continue  # Don't transfer to same exchange
                
                available = Decimal(str(balances.get(currency, 0)))
                
                # Check if this exchange has enough liquidity
                if available >= needed_amount and available > best_available:
                    best_source = exchange_name
                    best_available = available
            
            return best_source
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error finding source: {e}")
            return None
    
    async def _estimate_transfer_cost(self, currency: str, amount: Decimal) -> Decimal:
        """Estimate the cost of transferring liquidity"""
        try:
            # Simplified cost estimation
            # In production, this would consider network fees, exchange fees, etc.
            
            base_costs = {
                'USDT': Decimal('1.0'),   # $1 USDT transfer
                'USD': Decimal('0.0'),    # No cost for USD (internal)
                'BTC': Decimal('5.0'),    # $5 BTC transfer
                'ETH': Decimal('3.0'),    # $3 ETH transfer
                'SOL': Decimal('0.1'),    # $0.10 SOL transfer
                'ADA': Decimal('0.5'),    # $0.50 ADA transfer
                'DOT': Decimal('0.5'),    # $0.50 DOT transfer
            }
            
            return base_costs.get(currency, Decimal('1.0'))
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error estimating cost: {e}")
            return Decimal('1.0')
    
    async def _estimate_transfer_time(self, source_exchange: Optional[str], 
                                    target_exchange: str) -> float:
        """Estimate the time required for liquidity transfer"""
        try:
            if not source_exchange:
                return 3600.0  # 1 hour if no source
            
            if source_exchange == target_exchange:
                return 0.0  # No transfer needed
            
            # Simplified time estimation (in seconds)
            # In production, this would consider network confirmation times
            base_times = {
                ('coinbase', 'bybit'): 1800,  # 30 minutes
                ('bybit', 'coinbase'): 1800,  # 30 minutes
            }
            
            return base_times.get((source_exchange, target_exchange), 3600.0)  # 1 hour default
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error estimating time: {e}")
            return 3600.0
    
    async def _get_usd_value(self, currency: str, amount: float) -> Decimal:
        """Get USD value of a currency amount"""
        try:
            if currency in ['USD', 'USDT', 'USDC']:
                return Decimal(str(amount))
            
            # Try to get price from any available exchange
            for client in self.exchange_clients.values():
                try:
                    if hasattr(client, 'get_price'):
                        symbol = f"{currency}USDT"
                        price = client.get_price(symbol)
                        if price and float(price) > 0:
                            return Decimal(str(amount)) * Decimal(str(price))
                except Exception:
                    continue
            
            return Decimal('0')
            
        except Exception as e:
            logger.debug(f"Error getting USD value for {currency}: {e}")
            return Decimal('0')
    
    async def _convert_from_usd(self, currency: str, usd_amount: Decimal) -> Decimal:
        """Convert USD amount to currency amount"""
        try:
            if currency in ['USD', 'USDT', 'USDC']:
                return usd_amount
            
            # Try to get price from any available exchange
            for client in self.exchange_clients.values():
                try:
                    if hasattr(client, 'get_price'):
                        symbol = f"{currency}USDT"
                        price = client.get_price(symbol)
                        if price and float(price) > 0:
                            return usd_amount / Decimal(str(price))
                except Exception:
                    continue
            
            # Fallback to default conversion rates
            default_rates = {
                'BTC': Decimal('0.0005'),   # Assume $40,000 BTC
                'ETH': Decimal('0.01'),     # Assume $2,000 ETH
                'SOL': Decimal('0.5'),      # Assume $20 SOL
                'ADA': Decimal('50'),       # Assume $0.40 ADA
                'DOT': Decimal('5'),        # Assume $4 DOT
            }
            
            return usd_amount * default_rates.get(currency, Decimal('1'))
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error converting from USD: {e}")
            return usd_amount

    async def execute_liquidity_action(self, action: LiquidityAction) -> Dict[str, Any]:
        """Execute a liquidity management action"""
        try:
            logger.info(f"⚡ [LIQUIDITY-EXEC] Executing {action.action_type.value} action")
            logger.info(f"⚡ [LIQUIDITY-EXEC] {action.currency}: {action.amount:.6f}")

            if action.action_type == LiquidityActionType.PROVISION:
                result = await self._execute_provision(action)
            elif action.action_type == LiquidityActionType.EMERGENCY_PROVISION:
                result = await self._execute_emergency_provision(action)
            elif action.action_type == LiquidityActionType.WITHDRAWAL:
                result = await self._execute_withdrawal(action)
            elif action.action_type == LiquidityActionType.REBALANCE:
                result = await self._execute_rebalance(action)
            else:
                result = {"success": False, "error": f"Unknown action type: {action.action_type}"}

            # Update statistics
            self._update_liquidity_stats(action, result)

            return result

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY-EXEC] Error executing action: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_provision(self, action: LiquidityAction) -> Dict[str, Any]:
        """Execute liquidity provision action"""
        try:
            logger.info(f"💧 [PROVISION] Providing {action.amount:.6f} {action.currency}")

            if not action.source_exchange:
                return {"success": False, "error": "No source exchange available"}

            # For now, simulate the transfer
            # In production, this would execute actual transfers
            logger.info(f"💧 [PROVISION] Simulating transfer from {action.source_exchange} to {action.target_exchange}")

            # Simulate transfer time
            await asyncio.sleep(min(action.estimated_time / 100, 5))  # Max 5 seconds for testing

            # Update liquidity tracking
            self.liquidity_stats['total_provisions'] += 1

            return {
                "success": True,
                "action_type": "provision",
                "currency": action.currency,
                "amount": float(action.amount),
                "source": action.source_exchange,
                "target": action.target_exchange,
                "estimated_cost": float(action.estimated_cost)
            }

        except Exception as e:
            logger.error(f"❌ [PROVISION] Error executing provision: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_emergency_provision(self, action: LiquidityAction) -> Dict[str, Any]:
        """Execute emergency liquidity provision"""
        try:
            logger.warning(f"🚨 [EMERGENCY] Emergency provision of {action.amount:.6f} {action.currency}")

            # Emergency provisions have higher priority and faster execution
            if not action.source_exchange:
                # Try to find any available source
                action.source_exchange = await self._find_emergency_liquidity_source(action.currency, action.amount)

            if not action.source_exchange:
                return {"success": False, "error": "No emergency liquidity source available"}

            # Execute with higher priority
            logger.warning(f"🚨 [EMERGENCY] Executing emergency transfer from {action.source_exchange}")

            # Simulate faster emergency transfer
            await asyncio.sleep(1)  # 1 second for emergency

            # Update statistics
            self.liquidity_stats['emergency_provisions'] += 1

            return {
                "success": True,
                "action_type": "emergency_provision",
                "currency": action.currency,
                "amount": float(action.amount),
                "source": action.source_exchange,
                "target": action.target_exchange,
                "priority": "emergency"
            }

        except Exception as e:
            logger.error(f"❌ [EMERGENCY] Error executing emergency provision: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_withdrawal(self, action: LiquidityAction) -> Dict[str, Any]:
        """Execute liquidity withdrawal action"""
        try:
            logger.info(f"💸 [WITHDRAWAL] Withdrawing {action.amount:.6f} {action.currency}")

            # Simulate withdrawal
            await asyncio.sleep(2)  # 2 seconds for withdrawal

            # Update statistics
            self.liquidity_stats['total_withdrawals'] += 1

            return {
                "success": True,
                "action_type": "withdrawal",
                "currency": action.currency,
                "amount": float(action.amount),
                "source": action.target_exchange
            }

        except Exception as e:
            logger.error(f"❌ [WITHDRAWAL] Error executing withdrawal: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_rebalance(self, action: LiquidityAction) -> Dict[str, Any]:
        """Execute liquidity rebalancing action"""
        try:
            logger.info(f"⚖️ [REBALANCE] Rebalancing {action.amount:.6f} {action.currency}")

            # Simulate rebalancing
            await asyncio.sleep(3)  # 3 seconds for rebalancing

            return {
                "success": True,
                "action_type": "rebalance",
                "currency": action.currency,
                "amount": float(action.amount),
                "source": action.source_exchange,
                "target": action.target_exchange
            }

        except Exception as e:
            logger.error(f"❌ [REBALANCE] Error executing rebalance: {e}")
            return {"success": False, "error": str(e)}

    async def _find_emergency_liquidity_source(self, currency: str, amount: Decimal) -> Optional[str]:
        """Find emergency liquidity source with relaxed constraints"""
        try:
            # Look for any exchange with any amount of the currency
            for exchange_name, balances in self.current_liquidity['by_exchange'].items():
                available = Decimal(str(balances.get(currency, 0)))
                if available > 0:  # Any amount is acceptable for emergency
                    return exchange_name

            return None

        except Exception as e:
            logger.error(f"❌ [EMERGENCY] Error finding emergency source: {e}")
            return None

    def _update_liquidity_stats(self, action: LiquidityAction, result: Dict[str, Any]):
        """Update liquidity management statistics"""
        try:
            if result.get('success', False):
                # Update response time
                current_time = time.time()
                if hasattr(action, 'start_time'):
                    response_time = current_time - action.start_time
                    if self.liquidity_stats['average_response_time'] == 0:
                        self.liquidity_stats['average_response_time'] = response_time
                    else:
                        # Moving average
                        self.liquidity_stats['average_response_time'] = (
                            self.liquidity_stats['average_response_time'] * 0.9 + response_time * 0.1
                        )

            logger.info(f"📊 [LIQUIDITY-STATS] Provisions: {self.liquidity_stats['total_provisions']}")
            logger.info(f"📊 [LIQUIDITY-STATS] Withdrawals: {self.liquidity_stats['total_withdrawals']}")
            logger.info(f"📊 [LIQUIDITY-STATS] Emergency: {self.liquidity_stats['emergency_provisions']}")

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY-STATS] Error updating stats: {e}")

    async def monitor_liquidity_continuously(self):
        """Continuously monitor and manage liquidity"""
        try:
            logger.info("🚀 [LIQUIDITY] Starting continuous liquidity monitoring...")

            while True:
                try:
                    # Refresh liquidity assessment
                    await self.assess_current_liquidity()
                    await self.calculate_liquidity_requirements()

                    # Identify and execute urgent actions
                    actions = await self.identify_liquidity_needs()

                    if actions:
                        # Execute highest priority action
                        urgent_action = actions[0]

                        if urgent_action.priority >= 80:  # Critical or depleted
                            logger.warning(f"🚨 [LIQUIDITY] Executing urgent action: {urgent_action.reason}")
                            result = await self.execute_liquidity_action(urgent_action)

                            if result.get('success', False):
                                logger.info("✅ [LIQUIDITY] Urgent action executed successfully")
                            else:
                                logger.error(f"❌ [LIQUIDITY] Urgent action failed: {result.get('error')}")
                        else:
                            logger.info(f"ℹ️ [LIQUIDITY] Non-urgent action available: {urgent_action.reason}")
                    else:
                        logger.info("✅ [LIQUIDITY] All liquidity requirements satisfied")

                    # Wait before next check
                    await asyncio.sleep(300)  # 5 minute intervals

                except Exception as e:
                    logger.error(f"❌ [LIQUIDITY] Error in monitoring loop: {e}")
                    await asyncio.sleep(600)  # Wait longer on error

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Critical error in continuous monitoring: {e}")
            raise

    async def get_liquidity_status_report(self) -> Dict[str, Any]:
        """Generate comprehensive liquidity status report"""
        try:
            report = {
                'timestamp': time.time(),
                'total_exchanges': len(self.exchange_clients),
                'currencies_tracked': len(self.default_minimums),
                'liquidity_by_status': {},
                'critical_currencies': [],
                'recommendations': [],
                'statistics': self.liquidity_stats.copy()
            }

            # Analyze liquidity by status
            status_counts = {}
            critical_currencies = []

            for exchange_name, requirements in self.liquidity_requirements.items():
                for currency, requirement in requirements.items():
                    status = requirement.status.value
                    status_counts[status] = status_counts.get(status, 0) + 1

                    if requirement.status in [LiquidityStatus.CRITICAL, LiquidityStatus.DEPLETED]:
                        critical_currencies.append({
                            'currency': currency,
                            'exchange': exchange_name,
                            'status': status,
                            'current': float(requirement.current_balance),
                            'minimum': float(requirement.minimum_balance),
                            'deficit': float(requirement.minimum_balance - requirement.current_balance)
                        })

            report['liquidity_by_status'] = status_counts
            report['critical_currencies'] = critical_currencies

            # Generate recommendations
            if critical_currencies:
                report['recommendations'].append("Immediate liquidity provision needed for critical currencies")

            if status_counts.get('low', 0) > 0:
                report['recommendations'].append("Monitor low liquidity currencies for potential provision")

            if not report['recommendations']:
                report['recommendations'].append("Liquidity levels are adequate across all currencies")

            return report

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error generating status report: {e}")
            return {'error': str(e)}

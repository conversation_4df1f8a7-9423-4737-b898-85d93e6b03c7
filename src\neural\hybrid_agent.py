# neural/hybrid_agent.py
import torch
import numpy as np
from typing import Dict, Optional, List
import asyncio
from datetime import datetime, timezone
import hashlib
import logging
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa

# Local imports
from .dark_pool import <PERSON><PERSON><PERSON><PERSON>outer
from .market_monitor import CircuitBreaker
from .liquidity import LiquidityAnalyzer
from .arbitrage import CrossVenueArbitrageDetector
# FIXED: Removed merge conflict markers
try:
    from ..compliance.mifid import MiFIDIILogger
    from ..utils.secure_store import AuditVault
except ImportError:
    MiFIDIILogger = None
    AuditVault = None

try:
    from .som_memory import SOMemory
except ImportError:
    SOMemory = None

logger = logging.getLogger("institutional_agent")
# Import compliance components with fallback
try:
    from ..compliance.mifid import MiFIDIILogger
except ImportError:
    # Fallback MiFIDIILogger for standalone testing
    class MiFIDIILogger:
        def __init__(self, retention_days: int, encryption_key: str):
            self.retention = retention_days
            from cryptography.fernet import Fernet
            self.cipher = Fernet(encryption_key.encode() if isinstance(encryption_key, str) else encryption_key)
        def log(self, record): pass

try:
    from ..utils.secure_store import AuditVault
except ImportError:
    # Fallback AuditVault for standalone testing
    class AuditVault:
        def __init__(self, cloud_storage: bool, immutable_logging: bool): pass
        def store(self, record): pass

logger = logging.getLogger("institutional_agent")

# ---------------
# Support Classes - Define before HybridTradingAgent
# ---------------
class CircuitBreakerActivated(Exception):
    pass

class InsufficientLiquidity(Exception):
    pass

class DarkPoolRouter:
    def __init__(self, venues: List[str], allocation_strategy: str = 'balanced'):
        self.venues = venues
        self.strategies = {
            'balanced': self._balanced_allocation,
            'aggressive': self._aggressive_allocation,
            'round_robin': self._round_robin_allocation,
            'optimal': self._optimal_allocation  # Added missing optimal strategy
        }
        # Handle unknown strategies gracefully
        if allocation_strategy not in self.strategies:
            logger.warning(f"Unknown allocation strategy '{allocation_strategy}', using 'balanced'")
            allocation_strategy = 'balanced'
        self.allocation_strategy = self.strategies[allocation_strategy]
        
    def select_venue(self, action: Dict) -> str:
        return self.allocation_strategy(action)
    
    def _balanced_allocation(self, action):
        # Implementation for balanced dark pool allocation
        return self.venues[0] if self.venues else "default"
    
    def _aggressive_allocation(self, action):
        # Implementation for aggressive dark pool allocation
        return self.venues[-1] if self.venues else "default"
    
    def _round_robin_allocation(self, action):
        # Implementation for round-robin dark pool allocation
        if not self.venues:
            return "default"
        if not hasattr(self, '_current_venue_index'):
            self._current_venue_index = 0
        venue = self.venues[self._current_venue_index]
        self._current_venue_index = (self._current_venue_index + 1) % len(self.venues)
        return venue

    def _optimal_allocation(self, action):
        # Implementation for optimal dark pool allocation
        # For now, use balanced allocation as optimal
        # In production, this would analyze liquidity, fees, and execution quality
        return self._balanced_allocation(action)

>>>>>>> Stashed changes
class HybridTradingAgent:
    def __init__(self, config: Dict):
        self.config = config
        self._init_core_components()
        self._init_compliance_systems()
        self._init_risk_controls()
        self._init_arbitrage_engine()
<<<<<<< Updated upstream
=======
        # Dark pool configuration with defaults
        dark_pool_config = config.get('dark_pool', {
            'size_threshold': 100000,
            'venues': ['coinbase', 'bybit'],
            'allocation_strategy': 'optimal'
        })
        self.dark_pool_threshold = dark_pool_config.get('size_threshold', 100000)

        # Initialize DarkPoolRouter with proper parameters
        venues = dark_pool_config.get('venues', ['coinbase', 'bybit'])
        allocation_strategy = dark_pool_config.get('allocation_strategy', 'balanced')
        self.dark_pool_router = DarkPoolRouter(venues, allocation_strategy)
>>>>>>> Stashed changes
        
        # Dark pool configuration
        self.dark_pool_threshold = config['dark_pool']['size_threshold']
        self.dark_pool_router = DarkPoolRouter(
            config['dark_pool']['venues'],
            config['dark_pool']['allocation_strategy']
        )

    def _init_core_components(self):
        """Initialize main trading components"""
        self.lstm = torch.jit.load("lstm_processor.pt")
        self.som = SOMemory()
        self.rl_agent = PPO.load("ppo_trading.zip")
        self.alpha_net = AlphaNet().eval()

    def _init_compliance_systems(self):
        """MiFID II compliant systems"""
        # Get compliance config with safe defaults
        compliance_config = self.config.get('compliance', {})

        # Generate encryption key if not provided
        encryption_key = compliance_config.get('encryption_key')
        if not encryption_key:
            from cryptography.fernet import Fernet
            encryption_key = Fernet.generate_key().decode()
            logger.warning("[COMPLIANCE] No encryption key provided, generated new key")

        retention_days = compliance_config.get('retention_days', 7)

        self.audit_logger = MiFIDIILogger(
            retention_days=retention_days,
            encryption_key=encryption_key
        )
        self.audit_vault = AuditVault(
            cloud_storage=compliance_config.get('cloud_storage', True),
            immutable_logging=compliance_config.get('immutable_logging', True)
        )

    def _init_risk_controls(self):
        """Exchange circuit breakers and liquidity checks"""
        self.market_monitor = CircuitBreaker(
            volatility_threshold=0.15,
            price_drop_limit=0.05,
            recovery_period=300
        )
        self.liquidity_checker = LiquidityAnalyzer(
            min_liquidity_multiplier=1.5,
            slippage_tolerance=0.002
        )

    def _init_arbitrage_engine(self):
        """Cross-venue arbitrage detection"""
        self.arbitrage_detector = CrossVenueArbitrageDetector(
            venues=['binance', 'coinbase', 'kraken'],
            min_spread=0.0005,
            fee_adjusted=True
        )

    async def execute_order(self, action: Dict) -> Dict:
        """Enhanced institutional order execution pipeline"""
        try:
            # 1. Check market circuit breakers
            if self.market_monitor.breach_detected():
                raise CircuitBreakerActivated("Market volatility too high")
            
            # 2. Check liquidity conditions
            liquidity_ok = await self.liquidity_checker.validate(
                action['symbol'],
                action['amount']
            )
            if not liquidity_ok:
                action = self._adjust_order_for_liquidity(action)
            
            # 3. Dark pool routing decision
            if action['amount'] >= self.dark_pool_threshold:
                execution_venue = self.dark_pool_router.select_venue(action)
                action['venue'] = execution_venue
                action['dark_pool'] = True
            else:
                action['venue'] = 'lit_pool'
                action['dark_pool'] = False
                
            # 4. Arbitrage check
            arbitrage_opp = await self.arbitrage_detector.find_opportunity(
                action['symbol']
            )
            if arbitrage_opp:
                action = self._merge_arbitrage_action(action, arbitrage_opp)
            
            # 5. Execute with compliance logging
            result = await self._safe_execute(action)
            
            # 6. Post-trade processing
            await self._post_trade_updates(action, result)
            return result
            
        except CircuitBreakerActivated as e:
            logger.critical(f"Market shutdown: {str(e)}")
            return {'status': 'failed', 'reason': 'circuit_breaker'}

    async def _safe_execute(self, action: Dict) -> Dict:
        """Audited order execution with compliance checks"""
        audit_record = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'order_id': self._generate_order_id(action),
            'details': action,
            'pre_trade_checks': {
                'liquidity': self.liquidity_checker.last_check,
                'circuit_breaker': self.market_monitor.status()
            }
        }
        
        try:
            # Execute order through appropriate venue
            if action['dark_pool']:
                result = await self.dark_pool_router.execute(action)
            else:
                result = await self.lit_pool_executor.execute(action)
                
            # Log successful execution
            audit_record['result'] = result
            self.audit_logger.log(audit_record)
            self.audit_vault.store(audit_record)
            
            return result
            
        except Exception as e:
            audit_record['error'] = str(e)
            self.audit_logger.log(audit_record)
            self.audit_vault.store(audit_record)
            raise

    async def _post_trade_updates(self, action: Dict, result: Dict):
        """Update systems post-execution"""
        # Update market monitor with trade impact
        self.market_monitor.report_trade(
            symbol=action['symbol'],
            amount=action['amount'],
            price=result['price']
        )
        
        # Update liquidity model
        await self.liquidity_checker.update_liquidity_profile(
            action['symbol'],
            result['executed_qty']
        )
        
        # Refresh arbitrage opportunities
        await self.arbitrage_detector.refresh_data()

    def _adjust_order_for_liquidity(self, action: Dict) -> Dict:
        """Order slicing based on liquidity conditions"""
        max_size = self.liquidity_checker.get_max_order_size(action['symbol'])
        if max_size <= 0:
            raise InsufficientLiquidity("No available liquidity for execution")
            
        return {
            **action,
            'amount': min(action['amount'], max_size),
            'sliced': True,
            'original_amount': action['amount']
        }

    def _merge_arbitrage_action(self, action: Dict, arbitrage: Dict) -> Dict:
        """Combine base action with arbitrage opportunity"""
        return {
            **action,
            'arbitrage': True,
            'venue_pair': arbitrage['venues'],
            'spread': arbitrage['spread'],
            'target_price': arbitrage['target_price']
        }

    def _generate_order_id(self, action: Dict) -> str:
        """Generate MiFID II compliant order ID"""
        components = [
            action['symbol'],
            str(action['amount']),
            datetime.now(timezone.utc).isoformat(),
            action.get('venue', 'unknown')
        ]
        return hashlib.sha256('|'.join(components).encode()).hexdigest()

# ---------------
# Support Classes
# ---------------
class CircuitBreakerActivated(Exception):
    pass

class InsufficientLiquidity(Exception):
    pass

class DarkPoolRouter:
    def __init__(self, venues: List[str], allocation_strategy: str = 'balanced'):
        self.venues = venues
        self.strategies = {
            'balanced': self._balanced_allocation,
            'aggressive': self._aggressive_allocation
        }
        self.allocation_strategy = self.strategies[allocation_strategy]
        
    def select_venue(self, action: Dict) -> str:
        return self.allocation_strategy(action)
        
    def _balanced_allocation(self, action):
        # Implementation for balanced dark pool allocation
        pass

class CrossVenueArbitrageDetector:
    def __init__(self, venues: List[str], min_spread: float, fee_adjusted: bool):
        self.venues = venues
        self.min_spread = min_spread
        self.fee_adjusted = fee_adjusted
        
    async def find_opportunity(self, symbol: str) -> Optional[Dict]:
        # Implementation for cross-venue arbitrage detection
        pass

class MiFIDIILogger:
    def __init__(self, retention_days: int, encryption_key: str):
        self.retention = retention_days
        self.cipher = Fernet(encryption_key)
        
    def log(self, record: Dict):
        encrypted_record = self._encrypt_record(record)
        # Store in compliant storage
        pass
        
    def _encrypt_record(self, data: Dict) -> bytes:
        return self.cipher.encrypt(json.dumps(data).encode())

class AuditVault:
    def __init__(self, cloud_storage: bool, immutable_logging: bool):
        self.storage = CloudStorage() if cloud_storage else LocalStorage()
        self.immutable = immutable_logging
        
    def store(self, record: Dict):
        self.storage.save(record, immutable=self.immutable)
#!/usr/bin/env python3
"""
Main.py Integration Test for Multi-Pair Trading
Tests the actual main.py execution to ensure real-world functionality
"""

import asyncio
import os
import sys
import time
import signal
from pathlib import Path
from datetime import datetime

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# Set environment for testing
os.environ.update({
    'LIVE_TRADING': 'true',
    'REAL_MONEY_TRADING': 'true',
    'BYBIT_ONLY_MODE': 'true',
    'COINBASE_ENABLED': 'false'
})

class MainIntegrationTest:
    def __init__(self):
        self.test_duration = 60  # Run for 60 seconds
        self.start_time = None
        self.trades_executed = 0
        self.unique_pairs_traded = set()
        self.strategies_used = set()
        self.test_results = {
            'diversification_achieved': False,
            'multiple_strategies_used': False,
            'trades_executed': False,
            'system_stability': False
        }
    
    async def run_integration_test(self):
        """Run integration test with actual main.py"""
        print("🧪 [INTEGRATION] Starting Main.py Integration Test")
        print("=" * 60)
        
        try:
            # Import main function
            from main import main as main_function
            
            print(f"⏰ [INTEGRATION] Test will run for {self.test_duration} seconds")
            print("🎯 [INTEGRATION] Monitoring for multi-pair trading and strategy diversification")
            
            self.start_time = time.time()
            
            # Create a task to run main.py
            main_task = asyncio.create_task(main_function())
            
            # Create a task to monitor the test
            monitor_task = asyncio.create_task(self._monitor_test_progress())
            
            # Run both tasks with timeout
            try:
                await asyncio.wait_for(
                    asyncio.gather(main_task, monitor_task, return_exceptions=True),
                    timeout=self.test_duration + 10
                )
            except asyncio.TimeoutError:
                print("⏰ [INTEGRATION] Test completed - timeout reached")
            
            # Cancel tasks if still running
            if not main_task.done():
                main_task.cancel()
            if not monitor_task.done():
                monitor_task.cancel()
            
            # Analyze results
            await self._analyze_test_results()
            
        except Exception as e:
            print(f"❌ [INTEGRATION] Test failed with error: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def _monitor_test_progress(self):
        """Monitor test progress and collect metrics"""
        try:
            print("📊 [MONITOR] Starting test monitoring...")
            
            while time.time() - self.start_time < self.test_duration:
                await asyncio.sleep(5)  # Check every 5 seconds
                
                elapsed = time.time() - self.start_time
                remaining = self.test_duration - elapsed
                
                print(f"⏰ [MONITOR] Test progress: {elapsed:.0f}s elapsed, {remaining:.0f}s remaining")
                
                # Check for log files or other indicators of trading activity
                await self._check_trading_activity()
            
            print("✅ [MONITOR] Test monitoring completed")
            
        except Exception as e:
            print(f"❌ [MONITOR] Monitoring error: {e}")
    
    async def _check_trading_activity(self):
        """Check for signs of trading activity"""
        try:
            # Look for recent log files
            logs_dir = PROJECT_ROOT / "logs"
            if logs_dir.exists():
                today = datetime.now().strftime("%Y-%m-%d")
                today_logs = logs_dir / today
                
                if today_logs.exists():
                    # Check for trading logs
                    trading_logs = list(today_logs.glob("**/trading*.log"))
                    if trading_logs:
                        self.test_results['system_stability'] = True
                        
                        # Try to parse recent trading activity
                        for log_file in trading_logs[-3:]:  # Check last 3 log files
                            try:
                                with open(log_file, 'r', encoding='utf-8') as f:
                                    recent_lines = f.readlines()[-50:]  # Last 50 lines
                                    
                                    for line in recent_lines:
                                        if 'TRADE' in line.upper() and 'EXECUTED' in line.upper():
                                            self.trades_executed += 1
                                            self.test_results['trades_executed'] = True
                                        
                                        # Look for different trading pairs
                                        for pair in ['BTC', 'ETH', 'SOL', 'ADA', 'DOT']:
                                            if f'{pair}USDT' in line:
                                                self.unique_pairs_traded.add(f'{pair}USDT')
                                        
                                        # Look for different strategies
                                        for strategy in ['direct_currency_trading', 'triangular_arbitrage', 'cross_exchange_arbitrage', 'portfolio_rebalancing']:
                                            if strategy in line:
                                                self.strategies_used.add(strategy)
                            except Exception:
                                continue
            
            # Update test results
            if len(self.unique_pairs_traded) >= 3:
                self.test_results['diversification_achieved'] = True
            
            if len(self.strategies_used) >= 2:
                self.test_results['multiple_strategies_used'] = True
                
        except Exception as e:
            print(f"⚠️ [MONITOR] Error checking trading activity: {e}")
    
    async def _analyze_test_results(self):
        """Analyze and report test results"""
        print("\n📊 [RESULTS] Integration Test Analysis")
        print("=" * 60)
        
        print(f"⏰ [RESULTS] Test Duration: {self.test_duration} seconds")
        print(f"🔄 [RESULTS] Trades Executed: {self.trades_executed}")
        print(f"🎯 [RESULTS] Unique Pairs Traded: {len(self.unique_pairs_traded)}")
        print(f"📈 [RESULTS] Strategies Used: {len(self.strategies_used)}")
        
        if self.unique_pairs_traded:
            print(f"💱 [RESULTS] Trading Pairs: {list(self.unique_pairs_traded)}")
        
        if self.strategies_used:
            print(f"🎯 [RESULTS] Strategies: {list(self.strategies_used)}")
        
        # Evaluate test criteria
        print("\n🎯 [EVALUATION] Test Criteria Assessment:")
        
        criteria_results = []
        
        # Criterion 1: System Stability
        if self.test_results['system_stability']:
            print("✅ [CRITERION-1] System Stability: PASS - System ran without crashes")
            criteria_results.append(True)
        else:
            print("❌ [CRITERION-1] System Stability: FAIL - System instability detected")
            criteria_results.append(False)
        
        # Criterion 2: Trading Activity
        if self.test_results['trades_executed']:
            print("✅ [CRITERION-2] Trading Activity: PASS - Trades were executed")
            criteria_results.append(True)
        else:
            print("❌ [CRITERION-2] Trading Activity: FAIL - No trades detected")
            criteria_results.append(False)
        
        # Criterion 3: Pair Diversification
        if self.test_results['diversification_achieved']:
            print("✅ [CRITERION-3] Pair Diversification: PASS - Multiple pairs traded")
            criteria_results.append(True)
        else:
            print("❌ [CRITERION-3] Pair Diversification: FAIL - Limited pair diversity")
            criteria_results.append(False)
        
        # Criterion 4: Strategy Diversification
        if self.test_results['multiple_strategies_used']:
            print("✅ [CRITERION-4] Strategy Diversification: PASS - Multiple strategies used")
            criteria_results.append(True)
        else:
            print("❌ [CRITERION-4] Strategy Diversification: FAIL - Limited strategy diversity")
            criteria_results.append(False)
        
        # Overall Assessment
        passed_criteria = sum(criteria_results)
        total_criteria = len(criteria_results)
        success_rate = (passed_criteria / total_criteria) * 100
        
        print(f"\n🎯 [OVERALL] Criteria Passed: {passed_criteria}/{total_criteria} ({success_rate:.1f}%)")
        
        if success_rate >= 75:
            print("🎉 [OVERALL] INTEGRATION TEST: PASS")
            print("✅ [OVERALL] Multi-pair trading system is working correctly in production")
            return True
        else:
            print("⚠️ [OVERALL] INTEGRATION TEST: NEEDS IMPROVEMENT")
            print("❌ [OVERALL] Some issues detected in production environment")
            return False

async def main():
    """Main test execution"""
    print("🚀 Main.py Integration Test for Multi-Pair Trading")
    print("Testing actual main.py execution for real-world functionality")
    print("=" * 80)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check credentials
    if not os.getenv('BYBIT_API_KEY') or not os.getenv('BYBIT_API_SECRET'):
        print("❌ Missing Bybit API credentials")
        return 1
    
    # Run integration test
    test = MainIntegrationTest()
    success = await test.run_integration_test()
    
    if success:
        print("\n✅ Integration test completed successfully!")
        print("🎯 The main.py trading system is working correctly")
        return 0
    else:
        print("\n❌ Integration test detected issues!")
        print("⚠️ The main.py trading system needs improvements")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
